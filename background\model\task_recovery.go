package model

import (
	"encoding/json"
	"time"
)

// TaskRecovery 任务恢复记录
type TaskRecovery struct {
	Base
	TaskID           uint64     `json:"task_id,string" gorm:"index;not null"`
	StepID           uint64     `json:"step_id,string" gorm:"index"`        // 触发恢复的步骤ID
	EvaluationID     uint64     `json:"evaluation_id,string" gorm:"index"`  // 关联的评估ID
	RecoveryType     string     `json:"recovery_type" gorm:"size:50"`       // retry, replan, screenshot, manual
	RecoveryStatus   string     `json:"recovery_status" gorm:"size:20"`     // pending, processing, success, failed
	FailureReason    string     `json:"failure_reason" gorm:"type:text"`    // 原始失败原因
	RecoveryStrategy string     `json:"recovery_strategy" gorm:"type:text"` // 恢复策略描述
	RecoveryData     string     `json:"recovery_data" gorm:"type:text"`     // 恢复相关数据JSON
	AttemptCount     int        `json:"attempt_count" gorm:"default:1"`     // 尝试次数
	MaxAttempts      int        `json:"max_attempts" gorm:"default:3"`      // 最大尝试次数
	StartTime        time.Time  `json:"start_time"`                         // 恢复开始时间
	EndTime          *time.Time `json:"end_time"`                           // 恢复结束时间
	Result           string     `json:"result" gorm:"type:text"`            // 恢复结果
	NextStepPlan     string     `json:"next_step_plan" gorm:"type:text"`    // 下一步计划JSON

	// 注意：关联关系通过外键建立，避免循环引用
}

// 恢复类型常量
const (
	RecoveryTypeRetry      = "retry"      // 重试
	RecoveryTypeReplan     = "replan"     // 重新规划
	RecoveryTypeScreenshot = "screenshot" // 截图分析
	RecoveryTypeManual     = "manual"     // 人工干预
	RecoveryTypeSkip       = "skip"       // 跳过步骤
	RecoveryTypeAbort      = "abort"      // 终止任务
)

// 恢复状态常量
const (
	RecoveryStatusPending    = "pending"    // 等待处理
	RecoveryStatusProcessing = "processing" // 处理中
	RecoveryStatusSuccess    = "success"    // 恢复成功
	RecoveryStatusFailed     = "failed"     // 恢复失败
)

// GetRecoveryData 获取恢复数据
func (tr *TaskRecovery) GetRecoveryData() (map[string]interface{}, error) {
	if tr.RecoveryData == "" {
		return make(map[string]interface{}), nil
	}
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(tr.RecoveryData), &data); err != nil {
		return nil, err
	}
	return data, nil
}

// SetRecoveryData 设置恢复数据
func (tr *TaskRecovery) SetRecoveryData(data interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	tr.RecoveryData = string(jsonData)
	return nil
}

// GetNextStepPlan 获取下一步计划
func (tr *TaskRecovery) GetNextStepPlan() (map[string]interface{}, error) {
	if tr.NextStepPlan == "" {
		return make(map[string]interface{}), nil
	}
	var plan map[string]interface{}
	if err := json.Unmarshal([]byte(tr.NextStepPlan), &plan); err != nil {
		return nil, err
	}
	return plan, nil
}

// SetNextStepPlan 设置下一步计划
func (tr *TaskRecovery) SetNextStepPlan(plan interface{}) error {
	jsonData, err := json.Marshal(plan)
	if err != nil {
		return err
	}
	tr.NextStepPlan = string(jsonData)
	return nil
}

// CanRetry 判断是否可以重试
func (tr *TaskRecovery) CanRetry() bool {
	return tr.AttemptCount < tr.MaxAttempts
}

// IncrementAttempt 增加尝试次数
func (tr *TaskRecovery) IncrementAttempt() {
	tr.AttemptCount++
}

// IsCompleted 判断恢复是否完成
func (tr *TaskRecovery) IsCompleted() bool {
	return tr.RecoveryStatus == RecoveryStatusSuccess || tr.RecoveryStatus == RecoveryStatusFailed
}

// MarkCompleted 标记恢复完成
func (tr *TaskRecovery) MarkCompleted(success bool, result string) {
	now := time.Now()
	tr.EndTime = &now
	tr.Result = result
	if success {
		tr.RecoveryStatus = RecoveryStatusSuccess
	} else {
		tr.RecoveryStatus = RecoveryStatusFailed
	}
}
