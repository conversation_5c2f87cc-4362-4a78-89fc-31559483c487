package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"diandian/background/database"
	"diandian/background/domain"
	"diandian/background/model"
)

// TaskRecoveryEngine 任务恢复引擎
type TaskRecoveryEngine struct {
	automationService *AutomationService
	llmService        *LLMService
	visionService     *EnhancedVisionService
}

// NewTaskRecoveryEngine 创建任务恢复引擎
func NewTaskRecoveryEngine(automationService *AutomationService) *TaskRecoveryEngine {
	llmService := &LLMService{}
	return &TaskRecoveryEngine{
		automationService: automationService,
		llmService:        llmService,
		visionService:     NewEnhancedVisionService(llmService, automationService),
	}
}

// EvaluateStepResult 评估步骤执行结果
func (tre *TaskRecoveryEngine) EvaluateStepResult(ctx context.Context, step *model.Step, stepResult *domain.StepExecutionResult) (*model.StepEvaluation, error) {
	slog.Info("开始评估步骤结果", "step_id", step.ID, "step_type", step.ActionType)

	evaluation := &model.StepEvaluation{
		StepID:         step.ID,
		TaskID:         step.TaskID,
		EvaluationType: model.EvaluationTypeAuto,
		EvaluatedAt:    time.Now(),
	}

	// 基础评估：根据执行结果判断
	if stepResult.Success {
		evaluation.EvaluationResult = model.EvaluationResultSuccess
		evaluation.Confidence = 0.8
		evaluation.ActualState = "步骤执行成功"
	} else {
		evaluation.EvaluationResult = model.EvaluationResultFailure
		evaluation.Confidence = 0.9
		evaluation.FailureReason = stepResult.Error
		evaluation.ActualState = fmt.Sprintf("步骤执行失败: %s", stepResult.Error)
	}

	evaluation.ExpectedState = step.ExpectedOutcome

	// 如果需要更精确的评估，进行视觉分析
	if tre.needsVisualEvaluation(step, stepResult) {
		visualEval, err := tre.performVisualEvaluation(ctx, step, evaluation)
		if err != nil {
			slog.Warn("视觉评估失败", "error", err)
		} else {
			// 更新评估结果
			evaluation.EvaluationType = model.EvaluationTypeVisual
			evaluation.EvaluationResult = visualEval.Result
			evaluation.Confidence = visualEval.Confidence
			evaluation.ScreenshotPath = visualEval.ScreenshotPath
		}
	}

	// 确定恢复策略
	evaluation.RecoveryStrategy = tre.determineRecoveryStrategy(step, evaluation)

	// 保存评估结果
	if err := database.DB.Create(evaluation).Error; err != nil {
		return nil, fmt.Errorf("保存评估结果失败: %v", err)
	}

	slog.Info("步骤评估完成",
		"step_id", step.ID,
		"result", evaluation.EvaluationResult,
		"confidence", evaluation.Confidence,
		"recovery_strategy", evaluation.RecoveryStrategy)

	return evaluation, nil
}

// needsVisualEvaluation 判断是否需要视觉评估
func (tre *TaskRecoveryEngine) needsVisualEvaluation(step *model.Step, stepResult *domain.StepExecutionResult) bool {
	// 如果步骤涉及UI操作且执行失败，需要视觉评估
	uiActions := []string{"click", "type", "scroll"}
	for _, action := range uiActions {
		if step.ActionType == action && !stepResult.Success {
			return true
		}
	}

	// 如果期望结果描述了视觉状态，也需要视觉评估
	if step.ExpectedOutcome != "" && stepResult.Success {
		return true
	}

	return false
}

// VisualEvaluationResult 视觉评估结果
type VisualEvaluationResult struct {
	Result         string
	Confidence     float64
	ScreenshotPath string
	Analysis       string
}

// performVisualEvaluation 执行视觉评估
func (tre *TaskRecoveryEngine) performVisualEvaluation(ctx context.Context, step *model.Step, evaluation *model.StepEvaluation) (*VisualEvaluationResult, error) {
	// 截图
	screenshotResult := tre.automationService.GetEngine().Screenshot()
	if !screenshotResult.Success {
		return nil, fmt.Errorf("截图失败: %s", screenshotResult.Message)
	}

	screenshotPath, ok := screenshotResult.Data.(map[string]interface{})["screenshot_path"].(string)
	if !ok {
		return nil, fmt.Errorf("截图路径获取失败")
	}

	// 使用视觉服务分析当前活动显示器
	analysisResult, err := tre.visionService.AnalyzeActiveDisplay(step.ExpectedOutcome)
	if err != nil {
		return nil, fmt.Errorf("视觉分析失败: %v", err)
	}

	// 根据分析结果确定评估结果
	result := &VisualEvaluationResult{
		ScreenshotPath: screenshotPath,
		Analysis:       fmt.Sprintf("找到%d个元素，%d个操作建议", len(analysisResult.ElementsFound), len(analysisResult.Recommendations)),
	}

	// 根据找到的元素和建议判断是否匹配期望
	if len(analysisResult.ElementsFound) > 0 && len(analysisResult.Recommendations) > 0 {
		result.Result = model.EvaluationResultSuccess
		result.Confidence = 0.8
	} else {
		result.Result = model.EvaluationResultFailure
		result.Confidence = 0.6
	}

	return result, nil
}

// determineRecoveryStrategy 确定恢复策略
func (tre *TaskRecoveryEngine) determineRecoveryStrategy(step *model.Step, evaluation *model.StepEvaluation) string {
	// 如果评估成功，不需要恢复
	if evaluation.EvaluationResult == model.EvaluationResultSuccess {
		return ""
	}

	// 如果是可选步骤，可以跳过
	if step.Optional {
		return model.RecoveryStrategySkip
	}

	// 如果还可以重试，优先重试
	if step.CanRetry() {
		return model.RecoveryStrategyRetry
	}

	// 根据失败原因确定策略
	if evaluation.FailureReason != "" {
		// 网络相关错误，重试
		if containsAny(evaluation.FailureReason, []string{"timeout", "network", "connection"}) {
			return model.RecoveryStrategyRetry
		}

		// UI元素找不到，需要截图分析
		if containsAny(evaluation.FailureReason, []string{"element not found", "click failed", "not clickable"}) {
			return model.RecoveryStrategyScreenshot
		}

		// 复杂错误，需要重新规划
		if containsAny(evaluation.FailureReason, []string{"unexpected", "invalid", "error"}) {
			return model.RecoveryStrategyReplan
		}
	}

	// 默认策略：截图分析
	return model.RecoveryStrategyScreenshot
}

// containsAny 检查字符串是否包含任意一个子字符串
func containsAny(str string, substrings []string) bool {
	for _, substr := range substrings {
		if len(str) >= len(substr) {
			for i := 0; i <= len(str)-len(substr); i++ {
				if str[i:i+len(substr)] == substr {
					return true
				}
			}
		}
	}
	return false
}

// ExecuteRecovery 执行恢复操作
func (tre *TaskRecoveryEngine) ExecuteRecovery(ctx context.Context, evaluation *model.StepEvaluation) (*model.TaskRecovery, error) {
	slog.Info("开始执行恢复操作",
		"step_id", evaluation.StepID,
		"strategy", evaluation.RecoveryStrategy)

	recovery := &model.TaskRecovery{
		TaskID:           evaluation.TaskID,
		StepID:           evaluation.StepID,
		EvaluationID:     evaluation.ID,
		RecoveryType:     evaluation.RecoveryStrategy,
		RecoveryStatus:   model.RecoveryStatusPending,
		FailureReason:    evaluation.FailureReason,
		RecoveryStrategy: fmt.Sprintf("执行%s恢复策略", evaluation.RecoveryStrategy),
		StartTime:        time.Now(),
	}

	// 保存恢复记录
	if err := database.DB.Create(recovery).Error; err != nil {
		return nil, fmt.Errorf("保存恢复记录失败: %v", err)
	}

	// 根据策略执行恢复
	recovery.RecoveryStatus = model.RecoveryStatusProcessing
	database.DB.Save(recovery)

	var success bool
	var result string

	switch evaluation.RecoveryStrategy {
	case model.RecoveryStrategyRetry:
		success, result = tre.executeRetryRecovery(ctx, recovery)
	case model.RecoveryStrategySkip:
		success, result = tre.executeSkipRecovery(ctx, recovery)
	case model.RecoveryStrategyScreenshot:
		success, result = tre.executeScreenshotRecovery(ctx, recovery)
	case model.RecoveryStrategyReplan:
		success, result = tre.executeReplanRecovery(ctx, recovery)
	default:
		success = false
		result = "未知的恢复策略"
	}

	// 更新恢复结果
	recovery.MarkCompleted(success, result)
	database.DB.Save(recovery)

	slog.Info("恢复操作完成",
		"recovery_id", recovery.ID,
		"success", success,
		"result", result)

	return recovery, nil
}

// executeRetryRecovery 执行重试恢复
func (tre *TaskRecoveryEngine) executeRetryRecovery(ctx context.Context, recovery *model.TaskRecovery) (bool, string) {
	// 获取原始步骤
	var step model.Step
	if err := database.DB.First(&step, recovery.StepID).Error; err != nil {
		return false, fmt.Sprintf("获取步骤失败: %v", err)
	}

	// 检查是否可以重试
	if !step.CanRetry() {
		return false, "步骤已达到最大重试次数"
	}

	// 增加重试次数
	step.IncrementRetry()
	database.DB.Save(&step)

	// 等待一段时间后重试
	time.Sleep(2 * time.Second)

	// 重新执行步骤
	stepPlan := &domain.AutomationStepPlan{
		Type:                   step.ActionType,
		Description:            step.Content,
		Context:                step.ActionData,
		RequiresScreenAnalysis: true, // 重试时总是进行屏幕分析
		Optional:               step.Optional,
	}

	// 创建统一执行引擎来重新执行步骤
	engine := NewUnifiedExecutionEngine(tre.automationService)
	stepResult := engine.executeStepPlan(ctx, stepPlan)

	if stepResult.Success {
		// 更新步骤状态为成功
		step.MarkCompleted(true, stepResult.Message, "")
		database.DB.Save(&step)
		return true, fmt.Sprintf("步骤重试成功，当前重试次数: %d", step.RetryCount)
	} else {
		// 重试仍然失败
		step.ErrorMsg = fmt.Sprintf("重试失败: %s", stepResult.Error)
		database.DB.Save(&step)
		return false, fmt.Sprintf("步骤重试失败，当前重试次数: %d, 错误: %s", step.RetryCount, stepResult.Error)
	}
}

// executeSkipRecovery 执行跳过恢复
func (tre *TaskRecoveryEngine) executeSkipRecovery(ctx context.Context, recovery *model.TaskRecovery) (bool, string) {
	// 获取步骤并标记为跳过
	var step model.Step
	if err := database.DB.First(&step, recovery.StepID).Error; err != nil {
		return false, fmt.Sprintf("获取步骤失败: %v", err)
	}

	step.Status = model.StepStatusSkipped
	step.Result = "步骤已跳过（可选步骤）"
	database.DB.Save(&step)

	return true, "可选步骤已跳过，继续执行后续步骤"
}

// executeScreenshotRecovery 执行截图分析恢复
func (tre *TaskRecoveryEngine) executeScreenshotRecovery(ctx context.Context, recovery *model.TaskRecovery) (bool, string) {
	// 获取步骤信息
	var step model.Step
	if err := database.DB.First(&step, recovery.StepID).Error; err != nil {
		return false, fmt.Sprintf("获取步骤失败: %v", err)
	}

	// 截图分析当前状态
	screenshotResult := tre.automationService.GetEngine().Screenshot()
	if !screenshotResult.Success {
		return false, fmt.Sprintf("截图失败: %s", screenshotResult.Message)
	}

	screenshotPath, ok := screenshotResult.Data.(map[string]interface{})["screenshot_path"].(string)
	if !ok {
		return false, "截图路径获取失败"
	}

	// 使用视觉服务分析当前状态并提供建议
	visionResult, err := tre.visionService.AnalyzeActiveDisplay(step.ExpectedOutcome)
	if err != nil {
		return false, fmt.Sprintf("视觉分析失败: %v", err)
	}

	analysisResult := &ScreenshotAnalysisResult{
		Analysis:           fmt.Sprintf("找到%d个元素，%d个操作建议", len(visionResult.ElementsFound), len(visionResult.Recommendations)),
		MatchesExpectation: len(visionResult.ElementsFound) > 0,
		Confidence:         0.7,
		Suggestions:        make([]string, len(visionResult.Recommendations)),
	}

	// 转换操作建议
	for i, rec := range visionResult.Recommendations {
		analysisResult.Suggestions[i] = fmt.Sprintf("%s: %s (%s)", rec.Action, rec.Target, rec.Reason)
	}

	// 根据分析结果更新恢复数据
	recoveryData := map[string]interface{}{
		"screenshot_path": screenshotPath,
		"analysis":        analysisResult.Analysis,
		"suggestions":     analysisResult.Suggestions,
	}
	recovery.SetRecoveryData(recoveryData)

	// 如果分析提供了具体的操作建议，尝试执行第一个建议
	if len(analysisResult.Suggestions) > 0 {
		// 尝试执行第一个建议
		suggestion := analysisResult.Suggestions[0]
		slog.Info("尝试执行截图分析建议", "suggestion", suggestion)

		// 解析建议并转换为可执行的步骤
		stepPlan := tre.parseSuggestionToStepPlan(suggestion, &step)
		if stepPlan != nil {
			// 创建统一执行引擎来执行建议
			engine := NewUnifiedExecutionEngine(tre.automationService)
			stepResult := engine.executeStepPlan(ctx, stepPlan)

			if stepResult.Success {
				// 更新原步骤状态
				step.MarkCompleted(true, fmt.Sprintf("通过截图分析恢复成功: %s", suggestion), "")
				database.DB.Save(&step)
				return true, fmt.Sprintf("截图分析恢复成功，执行建议: %s", suggestion)
			} else {
				slog.Warn("执行截图分析建议失败", "error", stepResult.Error)
			}
		}

		return true, fmt.Sprintf("截图分析完成，发现%d个操作建议", len(analysisResult.Suggestions))
	}

	return false, "截图分析未找到有效的恢复方案"
}

// executeReplanRecovery 执行重新规划恢复
func (tre *TaskRecoveryEngine) executeReplanRecovery(ctx context.Context, recovery *model.TaskRecovery) (bool, string) {
	// 获取任务和当前步骤信息
	var task model.Task
	var step model.Step

	if err := database.DB.First(&task, recovery.TaskID).Error; err != nil {
		return false, fmt.Sprintf("获取任务失败: %v", err)
	}

	if err := database.DB.First(&step, recovery.StepID).Error; err != nil {
		return false, fmt.Sprintf("获取步骤失败: %v", err)
	}

	// 获取任务分解信息
	var taskDecomposition model.TaskDecomposition
	if err := database.DB.Where("task_id = ?", task.ID).First(&taskDecomposition).Error; err != nil {
		return false, fmt.Sprintf("获取任务分解失败: %v", err)
	}

	// 构建重新规划的提示
	prompt := fmt.Sprintf(`
任务描述: %s
当前失败步骤: %s (步骤%d)
失败原因: %s
期望结果: %s

请重新规划从当前步骤开始的后续执行步骤，考虑当前的失败情况。

要求：
1. 分析失败原因并提供替代方案
2. 生成新的步骤序列
3. 确保新步骤能够达到原始期望结果
4. 考虑当前屏幕状态和可用操作

请以JSON格式返回重新规划的步骤列表。
`, task.Description, step.Content, step.StepIndex, step.ErrorMsg, step.ExpectedOutcome)

	// 调用LLM重新规划
	response, err := tre.llmService.GenerateTaskReplan(prompt)
	if err != nil {
		slog.Error("LLM重新规划失败", "error", err)
		return false, fmt.Sprintf("重新规划失败: %v", err)
	}

	// 解析重新规划结果
	newSteps, err := tre.parseReplanResponse(response)
	if err != nil {
		slog.Error("解析重新规划结果失败", "error", err)
		return false, fmt.Sprintf("解析重新规划结果失败: %v", err)
	}

	// 创建新的步骤记录
	for i, newStepPlan := range newSteps {
		newStep := &model.Step{
			TaskID:          step.TaskID,
			StepIndex:       step.StepIndex + i + 1, // 从当前步骤后开始
			Content:         fmt.Sprintf("重新规划步骤 %d: %s", i+1, newStepPlan.Description),
			StepType:        model.StepTypeAction,
			Status:          model.StepStatusPending,
			ActionType:      newStepPlan.Type,
			ActionData:      newStepPlan.Context,
			ExpectedOutcome: newStepPlan.Description,
			Priority:        step.Priority,
			Optional:        newStepPlan.Optional,
			MaxRetries:      3,
			RetryCount:      0,
			Context:         fmt.Sprintf("重新规划步骤，原步骤ID: %d", step.ID),
		}

		if err := database.DB.Create(newStep).Error; err != nil {
			slog.Error("创建重新规划步骤失败", "error", err)
			continue
		}

		slog.Info("创建重新规划步骤", "new_step_id", newStep.ID, "type", newStep.ActionType)
	}

	// 标记原步骤为已跳过（因为有了新的替代步骤）
	step.Status = model.StepStatusSkipped
	step.Result = fmt.Sprintf("已重新规划，生成%d个新步骤", len(newSteps))
	database.DB.Save(&step)

	return true, fmt.Sprintf("重新规划成功，生成%d个新步骤", len(newSteps))
}

// ScreenshotAnalysisResult 截图分析结果
type ScreenshotAnalysisResult struct {
	Analysis           string                  `json:"analysis"`
	MatchesExpectation bool                    `json:"matches_expectation"`
	Confidence         float64                 `json:"confidence"`
	Suggestions        []string                `json:"suggestions"`
	Elements           []RecoveryVisualElement `json:"elements"`
}

// RecoveryVisualElement 恢复相关的视觉元素（避免与其他包冲突）
type RecoveryVisualElement struct {
	Type        string  `json:"type"`
	Description string  `json:"description"`
	X           int     `json:"x"`
	Y           int     `json:"y"`
	Width       int     `json:"width"`
	Height      int     `json:"height"`
	Confidence  float64 `json:"confidence"`
	Clickable   bool    `json:"clickable"`
}

// parseSuggestionToStepPlan 将建议解析为可执行的步骤计划
func (tre *TaskRecoveryEngine) parseSuggestionToStepPlan(suggestion string, originalStep *model.Step) *domain.AutomationStepPlan {
	// 简单的建议解析逻辑
	suggestion = strings.ToLower(strings.TrimSpace(suggestion))

	if strings.Contains(suggestion, "点击") || strings.Contains(suggestion, "click") {
		return &domain.AutomationStepPlan{
			Type:                   "click",
			Description:            fmt.Sprintf("根据截图分析建议执行点击: %s", suggestion),
			Context:                originalStep.ActionData,
			RequiresScreenAnalysis: true,
			Optional:               originalStep.Optional,
		}
	}

	if strings.Contains(suggestion, "输入") || strings.Contains(suggestion, "type") {
		return &domain.AutomationStepPlan{
			Type:                   "type",
			Description:            fmt.Sprintf("根据截图分析建议执行输入: %s", suggestion),
			Context:                originalStep.ActionData,
			RequiresScreenAnalysis: false,
			Optional:               originalStep.Optional,
		}
	}

	if strings.Contains(suggestion, "按键") || strings.Contains(suggestion, "key") {
		return &domain.AutomationStepPlan{
			Type:                   "key_press",
			Description:            fmt.Sprintf("根据截图分析建议执行按键: %s", suggestion),
			Context:                originalStep.ActionData,
			RequiresScreenAnalysis: false,
			Optional:               originalStep.Optional,
		}
	}

	// 默认返回点击操作
	return &domain.AutomationStepPlan{
		Type:                   "click",
		Description:            fmt.Sprintf("根据截图分析建议执行操作: %s", suggestion),
		Context:                originalStep.ActionData,
		RequiresScreenAnalysis: true,
		Optional:               originalStep.Optional,
	}
}

// parseReplanResponse 解析重新规划响应
func (tre *TaskRecoveryEngine) parseReplanResponse(response string) ([]*domain.AutomationStepPlan, error) {
	// 清理响应文本
	response = strings.TrimSpace(response)
	if strings.HasPrefix(response, "```json") {
		response = strings.TrimPrefix(response, "```json")
	}
	if strings.HasPrefix(response, "```") {
		response = strings.TrimPrefix(response, "```")
	}
	if strings.HasSuffix(response, "```") {
		response = strings.TrimSuffix(response, "```")
	}
	response = strings.TrimSpace(response)

	// 尝试解析为步骤数组
	var steps []*domain.AutomationStepPlan
	if err := json.Unmarshal([]byte(response), &steps); err != nil {
		// 如果直接解析失败，尝试解析为包含steps字段的对象
		var wrapper struct {
			Steps []*domain.AutomationStepPlan `json:"steps"`
		}
		if err2 := json.Unmarshal([]byte(response), &wrapper); err2 != nil {
			return nil, fmt.Errorf("JSON解析失败: %v, 原始错误: %v, 响应内容: %s", err2, err, response)
		}
		steps = wrapper.Steps
	}

	// 验证步骤
	validSteps := make([]*domain.AutomationStepPlan, 0)
	for i, step := range steps {
		if step.Type == "" {
			slog.Warn("跳过无效步骤：缺少类型", "step_index", i)
			continue
		}
		if step.Description == "" {
			step.Description = fmt.Sprintf("重新规划步骤 %d", i+1)
		}
		if step.Context == "" {
			step.Context = "{}"
		}
		validSteps = append(validSteps, step)
	}

	if len(validSteps) == 0 {
		return nil, fmt.Errorf("没有有效的重新规划步骤")
	}

	slog.Info("解析重新规划响应成功", "valid_steps", len(validSteps), "total_steps", len(steps))
	return validSteps, nil
}
