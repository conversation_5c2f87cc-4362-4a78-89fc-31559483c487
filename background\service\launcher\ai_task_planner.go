package launcher

import (
	"fmt"
	"log/slog"
	"time"
)

// AITaskPlannerImpl AI任务规划器实现
type AITaskPlannerImpl struct {
	llmService LLMService // 需要集成你现有的LLM服务
}

// LLMService LLM服务接口（需要与你现有的服务集成）
type LLMService interface {
	AnalyzeScreenForAppLaunch(screenshot []byte, appName string, screenAnalysis *ScreenAnalysis) (*LaunchPlan, error)
	ReplanAfterFailure(appName string, failedStep *LaunchStep, screenAnalysis *ScreenAnalysis) (*LaunchPlan, error)
}

// NewAITaskPlanner 创建AI任务规划器
func NewAITaskPlanner() AITaskPlanner {
	return &AITaskPlannerImpl{
		// 这里需要注入你现有的LLM服务
		llmService: nil, // 暂时为nil，需要集成
	}
}

// PlanAppLaunch 为应用启动制定计划
func (planner *AITaskPlannerImpl) PlanAppLaunch(appName string, screenAnalysis *ScreenAnalysis) (*LaunchPlan, error) {
	slog.Info("AI制定应用启动计划", "app", appName, "os", screenAnalysis.OperatingSystem)
	
	if planner.llmService == nil {
		// 如果没有LLM服务，使用内置的启发式规则
		return planner.createHeuristicPlan(appName, screenAnalysis)
	}
	
	// 使用LLM分析屏幕并制定计划
	plan, err := planner.llmService.AnalyzeScreenForAppLaunch(nil, appName, screenAnalysis)
	if err != nil {
		slog.Warn("LLM规划失败，使用启发式规则", "error", err)
		return planner.createHeuristicPlan(appName, screenAnalysis)
	}
	
	return plan, nil
}

// ExecutePlan 执行启动计划
func (planner *AITaskPlannerImpl) ExecutePlan(plan *LaunchPlan, controller MouseKeyboardController) error {
	slog.Info("开始执行启动计划", "app", plan.AppName, "steps", len(plan.Steps))
	
	for i, step := range plan.Steps {
		slog.Info("执行步骤", "step", i+1, "type", step.Type, "description", step.Description)
		
		err := planner.executeStep(&step, controller)
		if err != nil {
			slog.Error("步骤执行失败", "step", i+1, "error", err)
			
			// 如果有备用计划，尝试执行
			if plan.Fallback != nil {
				slog.Info("尝试备用计划")
				return planner.ExecutePlan(plan.Fallback, controller)
			}
			
			return fmt.Errorf("步骤 %d 执行失败: %v", i+1, err)
		}
		
		// 步骤间等待
		if step.WaitTime > 0 {
			controller.Wait(step.WaitTime)
		} else {
			controller.Wait(200 * time.Millisecond) // 默认等待
		}
	}
	
	slog.Info("启动计划执行完成", "app", plan.AppName)
	return nil
}

// ReplanOnFailure 失败后重新规划
func (planner *AITaskPlannerImpl) ReplanOnFailure(appName string, failedStep *LaunchStep, screenAnalysis *ScreenAnalysis) (*LaunchPlan, error) {
	slog.Info("失败后重新规划", "app", appName, "failed_step", failedStep.Type)
	
	if planner.llmService == nil {
		// 使用启发式重新规划
		return planner.createRecoveryPlan(appName, failedStep, screenAnalysis)
	}
	
	// 使用LLM重新规划
	plan, err := planner.llmService.ReplanAfterFailure(appName, failedStep, screenAnalysis)
	if err != nil {
		slog.Warn("LLM重新规划失败，使用启发式规则", "error", err)
		return planner.createRecoveryPlan(appName, failedStep, screenAnalysis)
	}
	
	return plan, nil
}

// executeStep 执行单个步骤
func (planner *AITaskPlannerImpl) executeStep(step *LaunchStep, controller MouseKeyboardController) error {
	switch step.Type {
	case "click":
		return controller.MoveMouse(step.X, step.Y)
	case "double_click":
		return controller.DoubleClick(step.X, step.Y)
	case "type":
		return controller.TypeText(step.Text)
	case "key_combo":
		return controller.PressKeyCombo(step.Keys)
	case "wait":
		return controller.Wait(step.WaitTime)
	case "click_and_type":
		return controller.ClickAndType(step.X, step.Y, step.Text)
	case "find_and_click":
		return planner.findAndClick(step, controller)
	case "analyze":
		return planner.analyzeAndDecide(step, controller)
	default:
		return fmt.Errorf("未知步骤类型: %s", step.Type)
	}
}

// findAndClick 查找元素并点击
func (planner *AITaskPlannerImpl) findAndClick(step *LaunchStep, controller MouseKeyboardController) error {
	if step.FindCondition == nil {
		return fmt.Errorf("查找条件为空")
	}
	
	// 这里需要集成屏幕分析器来查找元素
	// 暂时使用步骤中的坐标
	if step.X > 0 && step.Y > 0 {
		return controller.DoubleClick(step.X, step.Y)
	}
	
	return fmt.Errorf("无法找到目标元素")
}

// analyzeAndDecide 分析屏幕并决定下一步
func (planner *AITaskPlannerImpl) analyzeAndDecide(step *LaunchStep, controller MouseKeyboardController) error {
	// 这里需要重新截图分析，然后决定下一步操作
	slog.Info("分析屏幕并决定下一步", "description", step.Description)
	
	// 暂时返回成功
	return nil
}

// createHeuristicPlan 创建启发式计划（不依赖LLM）
func (planner *AITaskPlannerImpl) createHeuristicPlan(appName string, screenAnalysis *ScreenAnalysis) (*LaunchPlan, error) {
	slog.Info("创建启发式启动计划", "app", appName, "os", screenAnalysis.OperatingSystem)
	
	switch screenAnalysis.OperatingSystem {
	case "windows":
		return planner.createWindowsHeuristicPlan(appName, screenAnalysis)
	case "darwin":
		return planner.createMacOSHeuristicPlan(appName, screenAnalysis)
	case "linux":
		return planner.createLinuxHeuristicPlan(appName, screenAnalysis)
	default:
		return nil, fmt.Errorf("不支持的操作系统: %s", screenAnalysis.OperatingSystem)
	}
}

// createWindowsHeuristicPlan 创建Windows启发式计划
func (planner *AITaskPlannerImpl) createWindowsHeuristicPlan(appName string, screenAnalysis *ScreenAnalysis) (*LaunchPlan, error) {
	plan := &LaunchPlan{
		AppName:       appName,
		EstimatedTime: 5 * time.Second,
		Confidence:    0.7,
	}
	
	// 策略1：尝试在桌面找到应用图标
	if len(screenAnalysis.DesktopIcons) > 0 {
		for _, icon := range screenAnalysis.DesktopIcons {
			if icon.Text == appName || icon.IconName == appName {
				plan.Steps = []LaunchStep{
					{
						Type:        "double_click",
						Description: fmt.Sprintf("双击桌面上的%s图标", appName),
						X:           icon.X + icon.Width/2,
						Y:           icon.Y + icon.Height/2,
						WaitTime:    2 * time.Second,
					},
				}
				return plan, nil
			}
		}
	}
	
	// 策略2：使用开始菜单搜索
	if screenAnalysis.StartButtonLocation != nil {
		plan.Steps = []LaunchStep{
			{
				Type:        "click",
				Description: "点击开始按钮",
				X:           screenAnalysis.StartButtonLocation.X,
				Y:           screenAnalysis.StartButtonLocation.Y,
				WaitTime:    500 * time.Millisecond,
			},
			{
				Type:        "type",
				Description: fmt.Sprintf("输入应用名称: %s", appName),
				Text:        appName,
				WaitTime:    1 * time.Second,
			},
			{
				Type:        "key_combo",
				Description: "按回车启动应用",
				Keys:        []string{"Return"},
				WaitTime:    2 * time.Second,
			},
		}
		return plan, nil
	}
	
	// 策略3：使用Win+R运行对话框
	plan.Steps = []LaunchStep{
		{
			Type:        "key_combo",
			Description: "打开运行对话框",
			Keys:        []string{"Win", "r"},
			WaitTime:    500 * time.Millisecond,
		},
		{
			Type:        "type",
			Description: fmt.Sprintf("输入应用名称: %s", appName),
			Text:        appName,
			WaitTime:    500 * time.Millisecond,
		},
		{
			Type:        "key_combo",
			Description: "按回车启动应用",
			Keys:        []string{"Return"},
			WaitTime:    2 * time.Second,
		},
	}
	
	return plan, nil
}

// createMacOSHeuristicPlan 创建macOS启发式计划
func (planner *AITaskPlannerImpl) createMacOSHeuristicPlan(appName string, screenAnalysis *ScreenAnalysis) (*LaunchPlan, error) {
	plan := &LaunchPlan{
		AppName:       appName,
		EstimatedTime: 4 * time.Second,
		Confidence:    0.7,
	}
	
	// 策略1：使用Spotlight搜索
	plan.Steps = []LaunchStep{
		{
			Type:        "key_combo",
			Description: "打开Spotlight搜索",
			Keys:        []string{"Cmd", "Space"},
			WaitTime:    500 * time.Millisecond,
		},
		{
			Type:        "type",
			Description: fmt.Sprintf("输入应用名称: %s", appName),
			Text:        appName,
			WaitTime:    1 * time.Second,
		},
		{
			Type:        "key_combo",
			Description: "按回车启动应用",
			Keys:        []string{"Return"},
			WaitTime:    2 * time.Second,
		},
	}
	
	return plan, nil
}

// createLinuxHeuristicPlan 创建Linux启发式计划
func (planner *AITaskPlannerImpl) createLinuxHeuristicPlan(appName string, screenAnalysis *ScreenAnalysis) (*LaunchPlan, error) {
	plan := &LaunchPlan{
		AppName:       appName,
		EstimatedTime: 5 * time.Second,
		Confidence:    0.6,
	}
	
	// 策略1：使用Alt+F2启动器（适用于大多数桌面环境）
	plan.Steps = []LaunchStep{
		{
			Type:        "key_combo",
			Description: "打开应用启动器",
			Keys:        []string{"Alt", "F2"},
			WaitTime:    500 * time.Millisecond,
		},
		{
			Type:        "type",
			Description: fmt.Sprintf("输入应用名称: %s", appName),
			Text:        appName,
			WaitTime:    1 * time.Second,
		},
		{
			Type:        "key_combo",
			Description: "按回车启动应用",
			Keys:        []string{"Return"},
			WaitTime:    2 * time.Second,
		},
	}
	
	return plan, nil
}

// createRecoveryPlan 创建恢复计划
func (planner *AITaskPlannerImpl) createRecoveryPlan(appName string, failedStep *LaunchStep, screenAnalysis *ScreenAnalysis) (*LaunchPlan, error) {
	slog.Info("创建恢复计划", "app", appName, "failed_step", failedStep.Type)
	
	// 根据失败的步骤类型，尝试不同的恢复策略
	switch failedStep.Type {
	case "click", "double_click":
		// 如果点击失败，尝试键盘快捷键
		return planner.createKeyboardAlternativePlan(appName, screenAnalysis)
	case "type":
		// 如果输入失败，尝试重新获取焦点
		return planner.createRefocusAndTypePlan(appName, screenAnalysis)
	case "key_combo":
		// 如果快捷键失败，尝试鼠标操作
		return planner.createMouseAlternativePlan(appName, screenAnalysis)
	default:
		// 默认恢复策略：重新创建基础计划
		return planner.createHeuristicPlan(appName, screenAnalysis)
	}
}

// createKeyboardAlternativePlan 创建键盘替代方案
func (planner *AITaskPlannerImpl) createKeyboardAlternativePlan(appName string, screenAnalysis *ScreenAnalysis) (*LaunchPlan, error) {
	// 使用键盘快捷键作为替代方案
	return planner.createHeuristicPlan(appName, screenAnalysis)
}

// createRefocusAndTypePlan 创建重新获取焦点并输入的方案
func (planner *AITaskPlannerImpl) createRefocusAndTypePlan(appName string, screenAnalysis *ScreenAnalysis) (*LaunchPlan, error) {
	// 重新获取焦点然后输入
	return planner.createHeuristicPlan(appName, screenAnalysis)
}

// createMouseAlternativePlan 创建鼠标替代方案
func (planner *AITaskPlannerImpl) createMouseAlternativePlan(appName string, screenAnalysis *ScreenAnalysis) (*LaunchPlan, error) {
	// 使用鼠标操作作为替代方案
	return planner.createHeuristicPlan(appName, screenAnalysis)
}
