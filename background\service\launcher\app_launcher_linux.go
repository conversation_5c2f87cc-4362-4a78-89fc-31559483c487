//go:build linux

package launcher

import (
	"fmt"
	"log/slog"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

// LinuxLauncher Linux平台的应用启动器
type LinuxLauncher struct {
	PlatformLauncher
}

// newPlatformLauncher 创建Linux平台启动器
func newPlatformLauncher() AppLauncher {
	return &LinuxLauncher{}
}

// LaunchApp 智能启动应用（Linux实现）
func (ll *LinuxLauncher) LaunchApp(appIdentifier string) error {
	slog.Info("尝试启动Linux应用", "app", appIdentifier)

	// 特殊处理：浏览器启动
	if appIdentifier == "browser" {
		return ll.LaunchDefaultBrowser()
	}

	// 检查是否是第三方应用标记
	if strings.HasPrefix(appIdentifier, "THIRD_PARTY:") {
		appName := strings.TrimPrefix(appIdentifier, "THIRD_PARTY:")
		return ll.LaunchThirdPartyApp(appName)
	}

	// 尝试直接启动系统应用
	return ll.LaunchSystemApp(appIdentifier)
}

// LaunchSystemApp 启动系统内置应用
func (ll *LinuxLauncher) LaunchSystemApp(appName string) error {
	slog.Info("启动Linux系统应用", "app", appName)

	// 标准化应用名称
	normalizedName := ll.normalizeAppName(appName)

	// Linux系统应用映射
	systemApps := map[string]string{
		"notepad":    "gedit",
		"calc":       "gnome-calculator",
		"calculator": "gnome-calculator",
		"gedit":      "gedit",
		"kate":       "kate",
		"vim":        "vim",
		"nano":       "nano",
		"terminal":   "gnome-terminal",
		"konsole":    "konsole",
		"firefox":    "firefox",
	}

	if linuxApp, exists := systemApps[normalizedName]; exists {
		return ll.launchLinuxApp(linuxApp)
	}

	// 尝试直接启动
	return ll.launchLinuxApp(normalizedName)
}

// LaunchThirdPartyApp 启动第三方应用
func (ll *LinuxLauncher) LaunchThirdPartyApp(appName string) error {
	slog.Info("尝试启动Linux第三方应用", "app", appName)

	// 策略1: 尝试.desktop文件
	if path, err := ll.findAppInDesktopFiles(appName); err == nil && path != "" {
		slog.Info("通过.desktop文件找到应用", "app", appName, "path", path)
		return ll.launchDesktopFile(path)
	}

	// 策略2: 尝试PATH环境变量
	if path, err := ll.findAppInPATH(appName); err == nil && path != "" {
		slog.Info("通过PATH找到应用", "app", appName, "path", path)
		return ll.launchByPath(path)
	}

	// 策略3: 尝试常见安装路径
	if path, err := ll.findAppInCommonPaths(appName); err == nil && path != "" {
		slog.Info("通过常见路径找到应用", "app", appName, "path", path)
		return ll.launchByPath(path)
	}

	// 策略4: 尝试Snap应用
	if path, err := ll.findAppInSnap(appName); err == nil && path != "" {
		slog.Info("通过Snap找到应用", "app", appName, "path", path)
		return ll.launchSnapApp(appName)
	}

	// 策略5: 尝试Flatpak应用
	if path, err := ll.findAppInFlatpak(appName); err == nil && path != "" {
		slog.Info("通过Flatpak找到应用", "app", appName, "path", path)
		return ll.launchFlatpakApp(appName)
	}

	// 策略6: 使用跨平台搜索
	if path, err := ll.findAppWithSearch(appName); err == nil && path != "" {
		slog.Info("通过跨平台搜索找到应用", "app", appName, "path", path)
		return ll.launchByPath(path)
	}

	// 所有策略都失败，返回错误
	slog.Error("无法找到Linux第三方应用", "app", appName)
	return fmt.Errorf("无法找到应用: %s，建议检查应用是否已安装", appName)
}

// LaunchDefaultBrowser 启动默认浏览器（直接启动，避免递归）
func (ll *LinuxLauncher) LaunchDefaultBrowser() error {
	slog.Info("启动Linux默认浏览器（直接启动）")

	// 直接启动浏览器，避免递归调用退化机制
	urls := []string{
		"https://www.baidu.com",
		"https://www.google.com",
		"https://www.duckduckgo.com",
	}

	for _, url := range urls {
		cmd := exec.Command("xdg-open", url)
		err := cmd.Start()
		if err == nil {
			slog.Info("Linux默认浏览器启动成功", "url", url)
			return nil
		}
		slog.Debug("Linux浏览器URL失败", "url", url, "error", err)
	}

	slog.Error("Linux浏览器启动失败")
	return fmt.Errorf("启动默认浏览器失败")
}

// FindAppPath 查找应用程序路径
func (ll *LinuxLauncher) FindAppPath(appName string) (string, error) {
	// 尝试多种策略查找应用路径
	strategies := []func(string) (string, error){
		ll.findAppInDesktopFiles,
		ll.findAppInPATH,
		ll.findAppInCommonPaths,
		ll.findAppInSnap,
		ll.findAppInFlatpak,
		ll.findAppWithSearch,
	}

	for _, strategy := range strategies {
		if path, err := strategy(appName); err == nil && path != "" {
			return path, nil
		}
	}

	return "", fmt.Errorf("未找到应用: %s", appName)
}

// launchLinuxApp 启动Linux应用
func (ll *LinuxLauncher) launchLinuxApp(appName string) error {
	cmd := exec.Command(appName)
	err := cmd.Start()
	if err != nil {
		slog.Error("Linux应用启动失败", "app", appName, "error", err)
		return fmt.Errorf("启动应用失败: %v", err)
	}

	slog.Info("Linux应用启动成功", "app", appName, "pid", cmd.Process.Pid)
	return nil
}

// findAppInDesktopFiles 在.desktop文件中查找应用
func (ll *LinuxLauncher) findAppInDesktopFiles(appName string) (string, error) {
	desktopPaths := []string{
		"/usr/share/applications",
		"/usr/local/share/applications",
		filepath.Join(os.Getenv("HOME"), ".local/share/applications"),
	}

	for _, desktopPath := range desktopPaths {
		if path, err := ll.searchDesktopDirectory(desktopPath, appName); err == nil && path != "" {
			return path, nil
		}
	}

	return "", fmt.Errorf("未在.desktop文件中找到应用")
}

// searchDesktopDirectory 搜索desktop文件目录
func (ll *LinuxLauncher) searchDesktopDirectory(dirPath, appName string) (string, error) {
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return "", err
	}

	for _, entry := range entries {
		if strings.HasSuffix(entry.Name(), ".desktop") {
			fullPath := filepath.Join(dirPath, entry.Name())
			if ll.matchesDesktopFile(fullPath, appName) {
				return fullPath, nil
			}
		}
	}

	return "", fmt.Errorf("未找到匹配的.desktop文件")
}

// matchesDesktopFile 检查.desktop文件是否匹配应用名称
func (ll *LinuxLauncher) matchesDesktopFile(filePath, appName string) bool {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return false
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "Name=") {
			name := strings.TrimPrefix(line, "Name=")
			if strings.Contains(strings.ToLower(name), strings.ToLower(appName)) {
				return true
			}
		}
	}

	return false
}

// launchDesktopFile 启动.desktop文件
func (ll *LinuxLauncher) launchDesktopFile(desktopPath string) error {
	// 使用gtk-launch或xdg-open启动.desktop文件
	var cmd *exec.Cmd

	// 尝试gtk-launch
	if _, err := exec.LookPath("gtk-launch"); err == nil {
		desktopName := strings.TrimSuffix(filepath.Base(desktopPath), ".desktop")
		cmd = exec.Command("gtk-launch", desktopName)
	} else {
		// 回退到xdg-open
		cmd = exec.Command("xdg-open", desktopPath)
	}

	err := cmd.Start()
	if err != nil {
		return fmt.Errorf("启动.desktop文件失败: %v", err)
	}

	return nil
}

// findAppInPATH 在PATH环境变量中查找应用
func (ll *LinuxLauncher) findAppInPATH(appName string) (string, error) {
	path, err := exec.LookPath(appName)
	if err != nil {
		return "", fmt.Errorf("未在PATH中找到应用: %v", err)
	}
	return path, nil
}

// findAppInCommonPaths 在常见安装路径中查找应用
func (ll *LinuxLauncher) findAppInCommonPaths(appName string) (string, error) {
	commonPaths := []string{
		"/usr/bin",
		"/usr/local/bin",
		"/opt",
		"/usr/games",
		"/usr/local/games",
	}

	for _, commonPath := range commonPaths {
		if path, err := ll.searchInDirectory(commonPath, appName); err == nil && path != "" {
			return path, nil
		}
	}

	return "", fmt.Errorf("未在常见路径找到应用")
}

// findAppInSnap 在Snap应用中查找
func (ll *LinuxLauncher) findAppInSnap(appName string) (string, error) {
	// 检查snap是否可用
	if _, err := exec.LookPath("snap"); err != nil {
		return "", fmt.Errorf("snap不可用")
	}

	// 列出已安装的snap应用
	cmd := exec.Command("snap", "list")
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("获取snap列表失败: %v", err)
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(strings.ToLower(line), strings.ToLower(appName)) {
			fields := strings.Fields(line)
			if len(fields) > 0 {
				return "/snap/bin/" + fields[0], nil
			}
		}
	}

	return "", fmt.Errorf("未在snap中找到应用")
}

// findAppInFlatpak 在Flatpak应用中查找
func (ll *LinuxLauncher) findAppInFlatpak(appName string) (string, error) {
	// 检查flatpak是否可用
	if _, err := exec.LookPath("flatpak"); err != nil {
		return "", fmt.Errorf("flatpak不可用")
	}

	// 列出已安装的flatpak应用
	cmd := exec.Command("flatpak", "list", "--app")
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("获取flatpak列表失败: %v", err)
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(strings.ToLower(line), strings.ToLower(appName)) {
			fields := strings.Fields(line)
			if len(fields) > 1 {
				return fields[1], nil // 返回应用ID
			}
		}
	}

	return "", fmt.Errorf("未在flatpak中找到应用")
}

// launchSnapApp 启动Snap应用
func (ll *LinuxLauncher) launchSnapApp(appName string) error {
	cmd := exec.Command("snap", "run", appName)
	err := cmd.Start()
	if err != nil {
		return fmt.Errorf("启动snap应用失败: %v", err)
	}
	return nil
}

// launchFlatpakApp 启动Flatpak应用
func (ll *LinuxLauncher) launchFlatpakApp(appID string) error {
	cmd := exec.Command("flatpak", "run", appID)
	err := cmd.Start()
	if err != nil {
		return fmt.Errorf("启动flatpak应用失败: %v", err)
	}
	return nil
}

// searchInDirectory 在目录中搜索应用
func (ll *LinuxLauncher) searchInDirectory(dirPath, appName string) (string, error) {
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return "", err
	}

	for _, entry := range entries {
		if strings.Contains(strings.ToLower(entry.Name()), strings.ToLower(appName)) {
			fullPath := filepath.Join(dirPath, entry.Name())

			// 检查是否是可执行文件
			if info, err := entry.Info(); err == nil && info.Mode()&0111 != 0 {
				return fullPath, nil
			}
		}
	}

	return "", fmt.Errorf("未找到应用")
}
