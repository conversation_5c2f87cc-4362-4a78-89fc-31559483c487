package service

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"diandian/background/domain"
	"diandian/background/model"
)

// UnifiedExecutionEngine 统一的任务执行引擎
type UnifiedExecutionEngine struct {
	automationService *AutomationService
	llmService        *LLMService
	visionService     *EnhancedVisionService
	recoveryEngine    *TaskRecoveryEngine
	lifecycleManager  *StepLifecycleManager
}

// NewUnifiedExecutionEngine 创建统一的任务执行引擎
func NewUnifiedExecutionEngine(automationService *AutomationService) *UnifiedExecutionEngine {
	llmService := &LLMService{}
	visionService := NewEnhancedVisionService(llmService, automationService)
	recoveryEngine := NewTaskRecoveryEngine(automationService)
	lifecycleManager := NewStepLifecycleManager(automationService)

	return &UnifiedExecutionEngine{
		automationService: automationService,
		llmService:        llmService,
		visionService:     visionService,
		recoveryEngine:    recoveryEngine,
		lifecycleManager:  lifecycleManager,
	}
}

// ExecuteTaskDecomposition 执行任务分解结果
func (e *UnifiedExecutionEngine) ExecuteTaskDecomposition(ctx context.Context, taskID uint, decomposition *domain.AutomationTaskDecomposition) *domain.TaskExecutionResult {
	startTime := time.Now()

	result := &domain.TaskExecutionResult{
		TaskID:         taskID,
		Success:        false,
		TotalSteps:     len(decomposition.Steps),
		CompletedSteps: 0,
		StartTime:      startTime,
	}

	slog.Info("开始执行任务分解", "task_id", taskID, "step_count", len(decomposition.Steps))

	// 发送任务开始事件
	e.automationService.sendEvent(AutomationEvent{
		Type:    "task_started",
		TaskID:  taskID,
		Message: "统一任务执行开始",
		Data: map[string]interface{}{
			"task_type":  decomposition.TaskType,
			"step_count": len(decomposition.Steps),
			"risk_level": decomposition.RiskLevel,
		},
	})

	// 逐步执行
	for i, stepPlan := range decomposition.Steps {
		select {
		case <-ctx.Done():
			result.Message = "任务被取消"
			result.Error = "context cancelled"
			return result
		default:
		}

		slog.Info("执行步骤", "step", i+1, "type", stepPlan.Type, "description", stepPlan.Description)

		// 使用生命周期管理器创建步骤
		dbStep := e.lifecycleManager.CreateStep(taskID, i+1, &stepPlan)

		// 发送步骤开始事件
		e.automationService.sendEvent(AutomationEvent{
			Type:    "step_started",
			TaskID:  taskID,
			Message: fmt.Sprintf("执行步骤 %d: %s", i+1, stepPlan.Description),
			Data: map[string]interface{}{
				"step_index":               i,
				"step_type":                stepPlan.Type,
				"requires_screen_analysis": stepPlan.RequiresScreenAnalysis,
			},
		})

		// 执行单个步骤（带退化机制）
		stepResult := e.executeStepWithFallback(ctx, &stepPlan, dbStep)

		// 使用生命周期管理器更新步骤状态
		e.lifecycleManager.UpdateStepResult(dbStep, stepResult)

		// 如果步骤失败，进行恢复处理
		if !stepResult.Success {
			if stepPlan.Optional {
				slog.Warn("可选步骤失败，继续执行", "step", i+1, "error", stepResult.Error)

				// 发送步骤跳过事件
				e.automationService.sendEvent(AutomationEvent{
					Type:    "step_skipped",
					TaskID:  taskID,
					Message: fmt.Sprintf("步骤 %d 失败但为可选步骤，已跳过", i+1),
					Data: map[string]interface{}{
						"step_index": i,
						"error":      stepResult.Error,
					},
				})
			} else {
				// 尝试恢复
				recovered := e.attemptRecovery(ctx, dbStep, stepResult)
				if !recovered {
					result.Message = fmt.Sprintf("步骤 %d 执行失败且无法恢复: %s", i+1, stepResult.Error)
					result.Error = stepResult.Error
					result.CompletedSteps = i
					result.Duration = time.Since(startTime)

					// 发送步骤失败事件
					e.automationService.sendEvent(AutomationEvent{
						Type:    "step_failed",
						TaskID:  taskID,
						Message: fmt.Sprintf("步骤 %d 执行失败且无法恢复", i+1),
						Data: map[string]interface{}{
							"step_index": i,
							"error":      stepResult.Error,
						},
					})

					return result
				}
			}
		} else {
			// 步骤执行成功
			result.CompletedSteps++

			// 发送步骤完成事件
			e.automationService.sendEvent(AutomationEvent{
				Type:    "step_completed",
				TaskID:  taskID,
				Message: fmt.Sprintf("步骤 %d 执行成功", i+1),
				Data: map[string]interface{}{
					"step_index": i,
					"result":     stepResult.Data,
				},
			})
		}

		// 步骤间延迟
		time.Sleep(500 * time.Millisecond)
	}

	// 计算最终结果
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	result.Success = result.CompletedSteps == result.TotalSteps

	if result.Success {
		result.Message = "任务执行成功"
		// 发送任务完成事件
		e.automationService.sendEvent(AutomationEvent{
			Type:    "task_completed",
			TaskID:  taskID,
			Message: "任务执行成功",
			Data: map[string]interface{}{
				"completed_steps": result.CompletedSteps,
				"total_steps":     result.TotalSteps,
				"duration":        result.Duration.Milliseconds(),
			},
		})
	} else {
		if result.Message == "" {
			result.Message = fmt.Sprintf("任务部分完成，%d/%d 步骤成功", result.CompletedSteps, result.TotalSteps)
		}
		// 发送任务失败事件
		e.automationService.sendEvent(AutomationEvent{
			Type:    "task_failed",
			TaskID:  taskID,
			Message: result.Message,
			Data: map[string]interface{}{
				"completed_steps": result.CompletedSteps,
				"total_steps":     result.TotalSteps,
				"error":           result.Error,
			},
		})
	}

	slog.Info("任务执行完成",
		"task_id", taskID,
		"success", result.Success,
		"completed_steps", result.CompletedSteps,
		"total_steps", result.TotalSteps,
		"duration", result.Duration)

	return result
}

// executeStepWithFallback 执行步骤（带退化机制）
func (e *UnifiedExecutionEngine) executeStepWithFallback(ctx context.Context, stepPlan *domain.AutomationStepPlan, dbStep *model.Step) *domain.StepExecutionResult {
	// 第一层：正常执行
	result := e.executeStepPlan(ctx, stepPlan)
	if result.Success {
		return result
	}

	slog.Warn("步骤执行失败，开始退化处理", "step_type", stepPlan.Type, "error", result.Error)

	// 第二层：参数调整重试
	adjustedResult := e.executeWithAdjustedParameters(ctx, stepPlan, result.Error)
	if adjustedResult.Success {
		slog.Info("参数调整重试成功")
		return adjustedResult
	}

	// 第三层：替代方案执行
	alternativeResult := e.executeAlternativeApproach(ctx, stepPlan)
	if alternativeResult.Success {
		slog.Info("替代方案执行成功")
		return alternativeResult
	}

	// 第四层：保底操作（截屏分析+LLM生成操作）
	fallbackResult := e.executeFallbackOperation(ctx, stepPlan)
	if fallbackResult.Success {
		slog.Info("保底操作执行成功")
		return fallbackResult
	}

	// 所有退化方案都失败
	slog.Error("所有退化方案都失败", "step_type", stepPlan.Type)
	result.Error = fmt.Sprintf("原始错误: %s; 所有退化方案都失败", result.Error)
	return result
}

// attemptRecovery 尝试恢复失败的步骤
func (e *UnifiedExecutionEngine) attemptRecovery(ctx context.Context, step *model.Step, stepResult *domain.StepExecutionResult) bool {
	// 评估步骤结果
	evaluation, err := e.recoveryEngine.EvaluateStepResult(ctx, step, stepResult)
	if err != nil {
		slog.Error("步骤评估失败", "error", err)
		return false
	}

	// 如果不需要恢复，返回成功
	if !evaluation.NeedsRecovery() {
		return true
	}

	// 执行恢复操作
	recovery, err := e.recoveryEngine.ExecuteRecovery(ctx, evaluation)
	if err != nil {
		slog.Error("恢复操作失败", "error", err)
		return false
	}

	return recovery.RecoveryStatus == model.RecoveryStatusSuccess
}

// executeStepPlan 执行单个步骤计划
func (e *UnifiedExecutionEngine) executeStepPlan(ctx context.Context, stepPlan *domain.AutomationStepPlan) *domain.StepExecutionResult {
	result := &domain.StepExecutionResult{
		StepType:  stepPlan.Type,
		Success:   false,
		StartTime: time.Now(),
	}

	slog.Info("开始执行步骤",
		"type", stepPlan.Type,
		"description", stepPlan.Description,
		"requires_screen_analysis", stepPlan.RequiresScreenAnalysis)

	// 如果需要屏幕分析，先进行截图和分析
	var screenAnalysis *domain.VisualAnalysisResponse
	if stepPlan.RequiresScreenAnalysis {
		analysis, err := e.performScreenAnalysis(stepPlan.Context)
		if err != nil {
			slog.Warn("屏幕分析失败，继续执行", "error", err)
		} else {
			screenAnalysis = analysis
		}
	}

	// 根据步骤类型执行相应操作
	switch stepPlan.Type {
	case "click":
		result = e.executeClickStep(stepPlan, screenAnalysis)
	case "type":
		result = e.executeTypeStep(stepPlan)
	case "launch_app":
		result = e.executeLaunchAppStep(stepPlan)
	case "file":
		result = e.executeFileStep(stepPlan)
	case "screenshot":
		result = e.executeScreenshotStep(stepPlan)
	case "clipboard":
		result = e.executeClipboardStep(stepPlan)
	case "wait":
		result = e.executeWaitStep(stepPlan)
	case "key_press":
		result = e.executeKeyPressStep(stepPlan)
	default:
		result.Error = fmt.Sprintf("不支持的步骤类型: %s", stepPlan.Type)
	}

	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)

	return result
}

// performScreenAnalysis 执行屏幕分析
func (e *UnifiedExecutionEngine) performScreenAnalysis(context string) (*domain.VisualAnalysisResponse, error) {
	// 截图
	screenshotResult := e.automationService.GetEngine().Screenshot()
	if !screenshotResult.Success {
		return nil, fmt.Errorf("截图失败: %s", screenshotResult.Message)
	}

	// 从result中获取图像数据
	var imageData []byte
	if data, ok := screenshotResult.Data.(map[string]interface{}); ok {
		if dataStr, exists := data["data"]; exists {
			if dataBytes, ok := dataStr.([]byte); ok {
				imageData = dataBytes
			}
		}
	}

	if len(imageData) == 0 {
		return nil, fmt.Errorf("无法获取截图数据")
	}

	// 调用视觉分析
	analysis, err := e.llmService.AnalyzeScreenshot(imageData, context)
	if err != nil {
		return nil, fmt.Errorf("视觉分析失败: %v", err)
	}

	return analysis, nil
}

// executeWithAdjustedParameters 使用调整后的参数重试执行
func (e *UnifiedExecutionEngine) executeWithAdjustedParameters(ctx context.Context, stepPlan *domain.AutomationStepPlan, originalError string) *domain.StepExecutionResult {
	slog.Info("尝试参数调整重试", "step_type", stepPlan.Type, "original_error", originalError)

	// 根据错误类型调整参数
	adjustedPlan := e.adjustStepParameters(stepPlan, originalError)
	if adjustedPlan == nil {
		return &domain.StepExecutionResult{
			Success: false,
			Error:   "无法调整参数",
		}
	}

	// 重新执行
	return e.executeStepPlan(ctx, adjustedPlan)
}

// executeAlternativeApproach 执行替代方案
func (e *UnifiedExecutionEngine) executeAlternativeApproach(ctx context.Context, stepPlan *domain.AutomationStepPlan) *domain.StepExecutionResult {
	slog.Info("尝试替代方案执行", "step_type", stepPlan.Type)

	// 根据步骤类型选择替代方案
	switch stepPlan.Type {
	case "click":
		// 尝试使用键盘操作代替鼠标点击
		return e.executeKeyboardAlternative(stepPlan)
	case "type":
		// 尝试使用剪贴板输入
		return e.executeClipboardTypeAlternative(stepPlan)
	default:
		return &domain.StepExecutionResult{
			Success: false,
			Error:   fmt.Sprintf("步骤类型 %s 没有替代方案", stepPlan.Type),
		}
	}
}

// executeFallbackOperation 执行保底操作
func (e *UnifiedExecutionEngine) executeFallbackOperation(ctx context.Context, stepPlan *domain.AutomationStepPlan) *domain.StepExecutionResult {
	slog.Info("执行保底操作", "step_type", stepPlan.Type)

	// 截图分析当前状态
	analysis, err := e.performScreenAnalysis(stepPlan.Context)
	if err != nil {
		return &domain.StepExecutionResult{
			Success: false,
			Error:   fmt.Sprintf("保底操作截图分析失败: %v", err),
		}
	}

	// 使用LLM生成具体的鼠标键盘操作
	operation, err := e.generateFallbackOperation(stepPlan, analysis)
	if err != nil {
		return &domain.StepExecutionResult{
			Success: false,
			Error:   fmt.Sprintf("生成保底操作失败: %v", err),
		}
	}

	// 执行生成的操作
	return e.executeLLMGeneratedOperation(operation)
}
