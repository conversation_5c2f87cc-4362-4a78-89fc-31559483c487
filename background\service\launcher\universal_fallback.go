package launcher

import (
	"fmt"
	"log/slog"
	"os/exec"
	"runtime"
	"time"
)

// UniversalFallbackLauncher 通用退化重试启动器
// 这是任何应用启动都可以使用的退化系统
type UniversalFallbackLauncher struct {
	strategies     []LaunchStrategy
	mouseKeyboard  MouseKeyboardController
	screenAnalyzer ScreenAnalyzer
	aiPlanner      AITaskPlanner
}

// LaunchStrategy 通用启动策略接口
type LaunchStrategy interface {
	Name() string
	CanHandle(appIdentifier string) bool
	Launch(appIdentifier string) error
	Priority() int
}

// MouseKeyboardController 鼠标键盘控制器接口
// 这是最底层的操作能力，任何任务都可能需要
type MouseKeyboardController interface {
	// 鼠标操作
	MoveMouse(x, y int) error
	ClickMouse(button string) error // "left", "right", "middle"
	DoubleClick(x, y int) error
	DragMouse(fromX, fromY, toX, toY int) error

	// 键盘操作
	TypeText(text string) error
	PressKey(key string) error
	PressKey<PERSON>ombo(keys []string) error

	// 组合操作
	ClickAndType(x, y int, text string) error

	// 等待操作
	Wait(duration time.Duration) error
}

// ScreenAnalyzer 屏幕分析器接口
// 用于分析当前屏幕状态，为AI决策提供信息
type ScreenAnalyzer interface {
	CaptureScreen() ([]byte, error)
	FindElement(screenshot []byte, elementType string, elementName string) (*ElementInfo, error)
	AnalyzeScreen(screenshot []byte) (*ScreenAnalysis, error)
	FindText(screenshot []byte, text string) ([]*ElementInfo, error)
	FindIcon(screenshot []byte, iconName string) ([]*ElementInfo, error)
}

// AITaskPlanner AI任务规划器接口
// 当其他策略失败时，AI分析屏幕并制定多步操作计划
type AITaskPlanner interface {
	PlanAppLaunch(appName string, screenAnalysis *ScreenAnalysis) (*LaunchPlan, error)
	ExecutePlan(plan *LaunchPlan, controller MouseKeyboardController) error
	ReplanOnFailure(appName string, failedStep *LaunchStep, screenAnalysis *ScreenAnalysis) (*LaunchPlan, error)
}

// ElementInfo 屏幕元素信息
type ElementInfo struct {
	X, Y          int
	Width, Height int
	Confidence    float64
	ElementType   string // "button", "icon", "text", "input", "window"
	Text          string
	IconName      string
}

// ScreenAnalysis 屏幕分析结果
type ScreenAnalysis struct {
	Elements            []ElementInfo
	DesktopIcons        []ElementInfo
	TaskbarVisible      bool
	StartButtonLocation *ElementInfo
	SearchBoxLocation   *ElementInfo
	OperatingSystem     string
	ActiveWindows       []ElementInfo
	ScreenResolution    struct{ Width, Height int }
}

// LaunchPlan AI生成的启动计划
type LaunchPlan struct {
	AppName       string
	Steps         []LaunchStep
	EstimatedTime time.Duration
	Confidence    float64
	Fallback      *LaunchPlan // 如果当前计划失败的备用计划
}

// LaunchStep 启动步骤
type LaunchStep struct {
	Type        string // "click", "double_click", "type", "key_combo", "wait", "analyze", "find_and_click"
	Description string
	Parameters  map[string]interface{}

	// 坐标操作
	X, Y int

	// 文本操作
	Text string

	// 键盘操作
	Keys []string

	// 等待时间
	WaitTime time.Duration

	// 查找条件
	FindCondition *FindCondition

	// 验证条件
	VerifyCondition *VerifyCondition
}

// FindCondition 查找条件
type FindCondition struct {
	ElementType string // "icon", "text", "button"
	ElementName string
	Text        string
	Timeout     time.Duration
}

// VerifyCondition 验证条件
type VerifyCondition struct {
	ExpectedChange string // "window_opened", "app_started", "element_appeared"
	Timeout        time.Duration
}

// NewUniversalFallbackLauncher 创建通用退化启动器
func NewUniversalFallbackLauncher() *UniversalFallbackLauncher {
	ufl := &UniversalFallbackLauncher{
		mouseKeyboard:  NewMouseKeyboardController(),
		screenAnalyzer: NewScreenAnalyzer(),
		aiPlanner:      NewAITaskPlanner(),
	}

	// 注册策略，按优先级排序
	ufl.strategies = []LaunchStrategy{
		&NativeAPIStrategy{},        // 优先级1：原生API
		&SystemCommandStrategy{},    // 优先级2：系统命令
		&KeyboardShortcutStrategy{}, // 优先级3：键盘快捷键
		&AIVisualStrategy{ // 优先级4：AI视觉分析（最终保底）
			mouseKeyboard:  ufl.mouseKeyboard,
			screenAnalyzer: ufl.screenAnalyzer,
			aiPlanner:      ufl.aiPlanner,
		},
	}

	return ufl
}

// LaunchWithFallback 使用退化重试启动应用
func (ufl *UniversalFallbackLauncher) LaunchWithFallback(appIdentifier string) error {
	slog.Info("开始通用退化重试启动", "app", appIdentifier, "strategies", len(ufl.strategies))

	var lastError error

	for i, strategy := range ufl.strategies {
		if !strategy.CanHandle(appIdentifier) {
			slog.Debug("策略不支持此应用", "strategy", strategy.Name(), "app", appIdentifier)
			continue
		}

		slog.Info("尝试启动策略", "strategy", strategy.Name(), "priority", strategy.Priority(), "attempt", i+1)

		err := strategy.Launch(appIdentifier)
		if err == nil {
			slog.Info("启动成功", "strategy", strategy.Name(), "app", appIdentifier)
			return nil
		}

		slog.Warn("策略失败，尝试下一个", "strategy", strategy.Name(), "error", err)
		lastError = err

		// 策略间等待，避免冲突
		time.Sleep(500 * time.Millisecond)
	}

	return fmt.Errorf("所有策略都失败了，最后错误: %v", lastError)
}

// 策略实现占位符（需要在其他文件中实现）

// NativeAPIStrategy 原生API策略
type NativeAPIStrategy struct{}

func (s *NativeAPIStrategy) Name() string                        { return "NativeAPI" }
func (s *NativeAPIStrategy) Priority() int                       { return 1 }
func (s *NativeAPIStrategy) CanHandle(appIdentifier string) bool { return true }

func (s *NativeAPIStrategy) Launch(appIdentifier string) error {
	// 直接使用平台特定的原生API启动，避免递归
	launcher := newPlatformLauncher()

	// 根据应用类型调用不同的方法，避免通过LaunchApp造成递归
	switch appIdentifier {
	case "browser":
		// 直接调用系统浏览器启动，不使用退化机制
		return s.launchBrowserDirectly()
	default:
		// 对于其他应用，使用系统应用启动
		return launcher.LaunchSystemApp(appIdentifier)
	}
}

// launchBrowserDirectly 直接启动浏览器，不使用退化机制
func (s *NativeAPIStrategy) launchBrowserDirectly() error {
	slog.Info("直接启动浏览器（避免递归）")

	switch runtime.GOOS {
	case "windows":
		return s.launchBrowserWindows()
	case "darwin":
		return s.launchBrowserMacOS()
	case "linux":
		return s.launchBrowserLinux()
	default:
		return fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}
}

// 平台特定的浏览器启动方法
func (s *NativeAPIStrategy) launchBrowserWindows() error {
	slog.Info("Windows直接启动浏览器")

	// 尝试多个URL，从最可能成功的开始
	urls := []string{
		"https://www.baidu.com",
		"https://www.google.com",
		"https://www.bing.com",
	}

	for _, url := range urls {
		cmd := exec.Command("rundll32", "url.dll,FileProtocolHandler", url)
		err := cmd.Start()
		if err == nil {
			slog.Info("Windows浏览器启动成功", "url", url)
			return nil
		}
		slog.Debug("Windows浏览器URL失败", "url", url, "error", err)
	}

	return fmt.Errorf("Windows浏览器启动失败")
}

func (s *NativeAPIStrategy) launchBrowserMacOS() error {
	slog.Info("macOS直接启动浏览器")

	urls := []string{
		"https://www.baidu.com",
		"https://www.google.com",
		"https://www.apple.com",
	}

	for _, url := range urls {
		cmd := exec.Command("open", url)
		err := cmd.Start()
		if err == nil {
			slog.Info("macOS浏览器启动成功", "url", url)
			return nil
		}
		slog.Debug("macOS浏览器URL失败", "url", url, "error", err)
	}

	return fmt.Errorf("macOS浏览器启动失败")
}

func (s *NativeAPIStrategy) launchBrowserLinux() error {
	slog.Info("Linux直接启动浏览器")

	urls := []string{
		"https://www.baidu.com",
		"https://www.google.com",
		"https://www.duckduckgo.com",
	}

	for _, url := range urls {
		cmd := exec.Command("xdg-open", url)
		err := cmd.Start()
		if err == nil {
			slog.Info("Linux浏览器启动成功", "url", url)
			return nil
		}
		slog.Debug("Linux浏览器URL失败", "url", url, "error", err)
	}

	return fmt.Errorf("Linux浏览器启动失败")
}

// SystemCommandStrategy 系统命令策略
type SystemCommandStrategy struct{}

func (s *SystemCommandStrategy) Name() string                        { return "SystemCommand" }
func (s *SystemCommandStrategy) Priority() int                       { return 2 }
func (s *SystemCommandStrategy) CanHandle(appIdentifier string) bool { return true }

func (s *SystemCommandStrategy) Launch(appIdentifier string) error {
	// 这里需要实现系统命令启动逻辑
	return fmt.Errorf("系统命令策略需要实现")
}

// KeyboardShortcutStrategy 键盘快捷键策略
type KeyboardShortcutStrategy struct{}

func (s *KeyboardShortcutStrategy) Name() string                        { return "KeyboardShortcut" }
func (s *KeyboardShortcutStrategy) Priority() int                       { return 3 }
func (s *KeyboardShortcutStrategy) CanHandle(appIdentifier string) bool { return true }

func (s *KeyboardShortcutStrategy) Launch(appIdentifier string) error {
	// 这里需要实现键盘快捷键启动逻辑
	return fmt.Errorf("键盘快捷键策略需要实现")
}

// AIVisualStrategy AI视觉分析策略（最终保底）
type AIVisualStrategy struct {
	mouseKeyboard  MouseKeyboardController
	screenAnalyzer ScreenAnalyzer
	aiPlanner      AITaskPlanner
}

func (s *AIVisualStrategy) Name() string                        { return "AIVisual" }
func (s *AIVisualStrategy) Priority() int                       { return 4 }
func (s *AIVisualStrategy) CanHandle(appIdentifier string) bool { return true }

func (s *AIVisualStrategy) Launch(appIdentifier string) error {
	slog.Info("使用AI视觉分析策略（最终保底）", "app", appIdentifier)

	// 1. 截取屏幕
	screenshot, err := s.screenAnalyzer.CaptureScreen()
	if err != nil {
		return fmt.Errorf("截屏失败: %v", err)
	}

	// 2. 分析屏幕
	analysis, err := s.screenAnalyzer.AnalyzeScreen(screenshot)
	if err != nil {
		return fmt.Errorf("屏幕分析失败: %v", err)
	}

	// 3. AI制定启动计划
	plan, err := s.aiPlanner.PlanAppLaunch(appIdentifier, analysis)
	if err != nil {
		return fmt.Errorf("AI规划失败: %v", err)
	}

	// 4. 执行计划
	err = s.aiPlanner.ExecutePlan(plan, s.mouseKeyboard)
	if err != nil {
		return fmt.Errorf("计划执行失败: %v", err)
	}

	slog.Info("AI视觉策略执行成功", "app", appIdentifier, "steps", len(plan.Steps))
	return nil
}

// 构造函数在各自的文件中实现，这里不需要重复定义
