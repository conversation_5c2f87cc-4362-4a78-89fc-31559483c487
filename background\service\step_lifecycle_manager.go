package service

import (
	"fmt"
	"log/slog"
	"time"

	"diandian/background/database"
	"diandian/background/domain"
	"diandian/background/model"
)

// StepLifecycleManager 步骤生命周期管理器
type StepLifecycleManager struct {
	automationService *AutomationService
}

// NewStepLifecycleManager 创建步骤生命周期管理器
func NewStepLifecycleManager(automationService *AutomationService) *StepLifecycleManager {
	return &StepLifecycleManager{
		automationService: automationService,
	}
}

// CreateStep 创建步骤记录
func (slm *StepLifecycleManager) CreateStep(taskID uint, stepIndex int, stepPlan *domain.AutomationStepPlan) *model.Step {
	step := &model.Step{
		TaskID:          uint64(taskID),
		StepIndex:       stepIndex,
		Content:         fmt.Sprintf("步骤 %d: %s", stepIndex, stepPlan.Description),
		StepType:        model.StepTypeAction,
		Status:          model.StepStatusPending,
		ActionType:      stepPlan.Type,
		ActionData:      stepPlan.Context,
		ExpectedOutcome: stepPlan.Description,
		Priority:        5, // 默认优先级
		Optional:        stepPlan.Optional,
		MaxRetries:      3, // 默认最大重试次数
		RetryCount:      0,
	}

	// 保存到数据库
	if err := database.DB.Create(step).Error; err != nil {
		slog.Error("创建步骤记录失败", "error", err)
		return nil
	}

	slog.Info("创建步骤记录", "step_id", step.ID, "step_index", stepIndex, "type", stepPlan.Type)
	return step
}

// StartStep 开始执行步骤
func (slm *StepLifecycleManager) StartStep(step *model.Step) {
	step.MarkStarted()
	if err := database.DB.Save(step).Error; err != nil {
		slog.Error("更新步骤开始状态失败", "step_id", step.ID, "error", err)
	}
	slog.Info("步骤开始执行", "step_id", step.ID, "type", step.ActionType)
}

// UpdateStepResult 更新步骤执行结果
func (slm *StepLifecycleManager) UpdateStepResult(step *model.Step, result *domain.StepExecutionResult) {
	var resultMsg, errorMsg string
	
	if result.Success {
		resultMsg = "执行成功"
		if result.Message != "" {
			resultMsg = result.Message
		}
	} else {
		errorMsg = result.Error
		resultMsg = "执行失败"
	}

	step.MarkCompleted(result.Success, resultMsg, errorMsg)

	// 如果有截图路径，保存它
	if result.ScreenshotPath != "" {
		step.Screenshot = result.ScreenshotPath
	}

	// 保存执行数据
	if result.Data != nil {
		if metadata, err := step.GetMetadata(); err == nil {
			metadata["execution_data"] = result.Data
			step.SetMetadata(metadata)
		}
	}

	if err := database.DB.Save(step).Error; err != nil {
		slog.Error("更新步骤结果失败", "step_id", step.ID, "error", err)
	}

	slog.Info("步骤执行完成",
		"step_id", step.ID,
		"success", result.Success,
		"duration", result.Duration,
		"error", errorMsg)
}

// CreateFallbackStep 创建退化步骤记录
func (slm *StepLifecycleManager) CreateFallbackStep(originalStep *model.Step, fallbackType string, description string) *model.Step {
	fallbackStep := &model.Step{
		TaskID:          originalStep.TaskID,
		StepIndex:       originalStep.StepIndex,
		Content:         fmt.Sprintf("退化步骤 (%s): %s", fallbackType, description),
		StepType:        model.StepTypeAction,
		Status:          model.StepStatusPending,
		ActionType:      fmt.Sprintf("%s_fallback", originalStep.ActionType),
		ActionData:      originalStep.ActionData,
		ExpectedOutcome: originalStep.ExpectedOutcome,
		Priority:        originalStep.Priority + 1, // 退化步骤优先级稍低
		Optional:        originalStep.Optional,
		MaxRetries:      1, // 退化步骤通常只重试一次
		RetryCount:      0,
		Context:         fmt.Sprintf("原始步骤ID: %d, 退化类型: %s", originalStep.ID, fallbackType),
	}

	// 设置元数据
	metadata := map[string]interface{}{
		"original_step_id": originalStep.ID,
		"fallback_type":    fallbackType,
		"fallback_level":   slm.getFallbackLevel(fallbackType),
	}
	fallbackStep.SetMetadata(metadata)

	// 保存到数据库
	if err := database.DB.Create(fallbackStep).Error; err != nil {
		slog.Error("创建退化步骤记录失败", "error", err)
		return nil
	}

	slog.Info("创建退化步骤记录",
		"fallback_step_id", fallbackStep.ID,
		"original_step_id", originalStep.ID,
		"fallback_type", fallbackType)

	return fallbackStep
}

// getFallbackLevel 获取退化级别
func (slm *StepLifecycleManager) getFallbackLevel(fallbackType string) int {
	switch fallbackType {
	case "parameter_adjustment":
		return 1
	case "alternative_approach":
		return 2
	case "screenshot_analysis":
		return 3
	case "manual_intervention":
		return 4
	default:
		return 0
	}
}

// MarkStepSkipped 标记步骤为跳过
func (slm *StepLifecycleManager) MarkStepSkipped(step *model.Step, reason string) {
	step.Status = model.StepStatusSkipped
	step.Result = fmt.Sprintf("步骤已跳过: %s", reason)
	
	if err := database.DB.Save(step).Error; err != nil {
		slog.Error("标记步骤跳过失败", "step_id", step.ID, "error", err)
	}

	slog.Info("步骤已跳过", "step_id", step.ID, "reason", reason)
}

// IncrementRetryCount 增加重试次数
func (slm *StepLifecycleManager) IncrementRetryCount(step *model.Step) bool {
	if !step.CanRetry() {
		return false
	}

	step.IncrementRetry()
	step.Status = model.StepStatusPending // 重置为待执行状态

	if err := database.DB.Save(step).Error; err != nil {
		slog.Error("增加重试次数失败", "step_id", step.ID, "error", err)
		return false
	}

	slog.Info("步骤重试次数增加", "step_id", step.ID, "retry_count", step.RetryCount)
	return true
}

// GetStepHistory 获取步骤执行历史
func (slm *StepLifecycleManager) GetStepHistory(taskID uint64) ([]*model.Step, error) {
	var steps []*model.Step
	err := database.DB.Where("task_id = ?", taskID).Order("step_index ASC, created_at ASC").Find(&steps).Error
	if err != nil {
		return nil, fmt.Errorf("获取步骤历史失败: %v", err)
	}
	return steps, nil
}

// GetFailedSteps 获取失败的步骤
func (slm *StepLifecycleManager) GetFailedSteps(taskID uint64) ([]*model.Step, error) {
	var steps []*model.Step
	err := database.DB.Where("task_id = ? AND status = ?", taskID, model.StepStatusFailed).Find(&steps).Error
	if err != nil {
		return nil, fmt.Errorf("获取失败步骤失败: %v", err)
	}
	return steps, nil
}

// GetRetryableSteps 获取可重试的步骤
func (slm *StepLifecycleManager) GetRetryableSteps(taskID uint64) ([]*model.Step, error) {
	var steps []*model.Step
	err := database.DB.Where("task_id = ? AND status = ? AND retry_count < max_retries", 
		taskID, model.StepStatusFailed).Find(&steps).Error
	if err != nil {
		return nil, fmt.Errorf("获取可重试步骤失败: %v", err)
	}
	return steps, nil
}

// CleanupOldSteps 清理旧的步骤记录
func (slm *StepLifecycleManager) CleanupOldSteps(olderThanDays int) error {
	cutoffTime := time.Now().AddDate(0, 0, -olderThanDays)
	
	result := database.DB.Where("created_at < ?", cutoffTime).Delete(&model.Step{})
	if result.Error != nil {
		return fmt.Errorf("清理旧步骤记录失败: %v", result.Error)
	}

	slog.Info("清理旧步骤记录完成", "deleted_count", result.RowsAffected, "cutoff_time", cutoffTime)
	return nil
}

// GetStepStatistics 获取步骤统计信息
func (slm *StepLifecycleManager) GetStepStatistics(taskID uint64) (*StepStatistics, error) {
	var stats StepStatistics
	
	// 总步骤数
	database.DB.Model(&model.Step{}).Where("task_id = ?", taskID).Count(&stats.TotalSteps)
	
	// 成功步骤数
	database.DB.Model(&model.Step{}).Where("task_id = ? AND status = ?", taskID, model.StepStatusCompleted).Count(&stats.SuccessfulSteps)
	
	// 失败步骤数
	database.DB.Model(&model.Step{}).Where("task_id = ? AND status = ?", taskID, model.StepStatusFailed).Count(&stats.FailedSteps)
	
	// 跳过步骤数
	database.DB.Model(&model.Step{}).Where("task_id = ? AND status = ?", taskID, model.StepStatusSkipped).Count(&stats.SkippedSteps)
	
	// 计算成功率
	if stats.TotalSteps > 0 {
		stats.SuccessRate = float64(stats.SuccessfulSteps) / float64(stats.TotalSteps)
	}

	return &stats, nil
}

// StepStatistics 步骤统计信息
type StepStatistics struct {
	TotalSteps      int64   `json:"total_steps"`
	SuccessfulSteps int64   `json:"successful_steps"`
	FailedSteps     int64   `json:"failed_steps"`
	SkippedSteps    int64   `json:"skipped_steps"`
	SuccessRate     float64 `json:"success_rate"`
}
