package model

import "time"

type Task struct {
	Base
	ConversationID uint64     `json:"conversation_id,string,omitempty" gorm:"index"`
	Name           string     `json:"name" gorm:"size:200"`
	Description    string     `json:"description" gorm:"type:text"`
	Status         string     `json:"status" gorm:"size:50"`      // pending, running, completed, failed, cancelled
	Progress       int        `json:"progress" gorm:"default:0"`  // 0-100
	Priority       int        `json:"priority" gorm:"default:5"`  // 1-10 任务优先级
	StartTime      *time.Time `json:"start_time"`                 // 任务开始时间
	EndTime        *time.Time `json:"end_time"`                   // 任务结束时间
	Result         string     `json:"result" gorm:"type:text"`    // 任务执行结果
	ErrorMsg       string     `json:"error_msg" gorm:"type:text"` // 最后的错误信息

	// 注意：关联关系通过外键建立，避免循环引用，在需要时通过查询获取
}

// 任务状态常量
const (
	TaskStatusPending   = "pending"
	TaskStatusRunning   = "running"
	TaskStatusCompleted = "completed"
	TaskStatusFailed    = "failed"
	TaskStatusCancelled = "cancelled"
)
