//go:build linux

package appsearch

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// LinuxAppSearcher Linux平台的应用搜索器
type LinuxAppSearcher struct {
	cache      map[string]*AppInfo
	cacheTime  time.Time
	cacheMutex sync.RWMutex
}

// newPlatformSearcher 创建Linux平台搜索器
func newPlatformSearcher() AppSearcher {
	return &LinuxAppSearcher{
		cache: make(map[string]*AppInfo),
	}
}

// SearchApps 搜索应用程序
func (l *LinuxAppSearcher) SearchApps(ctx context.Context, query string, options *SearchOptions) *SearchResult {
	start := time.Now()
	result := &SearchResult{
		Apps:     make([]*AppInfo, 0),
		Strategy: "linux_multi_strategy",
	}

	// 设置默认搜索路径
	if len(options.SearchPaths) == 0 {
		options.SearchPaths = []string{
			"/usr/share/applications",
			"/usr/local/share/applications",
			filepath.Join(os.Getenv("HOME"), ".local/share/applications"),
			"/usr/bin",
			"/usr/local/bin",
			"/opt",
			"/snap/bin",
			"/var/lib/flatpak/exports/bin",
		}
	}

	var allApps []*AppInfo

	// 策略1: .desktop文件解析
	if options.UseRegistry {
		if desktopApps := l.searchByDesktopFiles(ctx, query, options.SearchPaths); len(desktopApps) > 0 {
			allApps = append(allApps, desktopApps...)
			slog.Debug("Desktop文件搜索找到应用", "count", len(desktopApps))
		}
	}

	// 策略2: PATH环境变量搜索
	if options.UseFileSystem {
		if pathApps := l.searchByPATH(ctx, query); len(pathApps) > 0 {
			allApps = append(allApps, pathApps...)
			slog.Debug("PATH搜索找到应用", "count", len(pathApps))
		}
	}

	// 策略3: 包管理器集成
	if options.UsePackageManager {
		if pkgApps := l.searchByPackageManager(ctx, query); len(pkgApps) > 0 {
			allApps = append(allApps, pkgApps...)
			slog.Debug("包管理器搜索找到应用", "count", len(pkgApps))
		}
	}

	// 策略4: 文件系统搜索
	if options.UseFileSystem {
		if fsApps := l.searchByFileSystem(ctx, query, options.SearchPaths); len(fsApps) > 0 {
			allApps = append(allApps, fsApps...)
			slog.Debug("文件系统搜索找到应用", "count", len(fsApps))
		}
	}

	// 去重和排序
	uniqueApps := l.deduplicateApps(allApps)
	
	// 计算置信度并过滤
	for _, app := range uniqueApps {
		confidence := CalculateConfidence(query, app)
		if confidence >= options.MinConfidence {
			app.Confidence = confidence
			result.Apps = append(result.Apps, app)
		}
	}

	// 按置信度排序
	l.sortAppsByConfidence(result.Apps)

	// 限制结果数量
	if options.MaxResults > 0 && len(result.Apps) > options.MaxResults {
		result.Apps = result.Apps[:options.MaxResults]
	}

	result.Duration = time.Since(start)
	return result
}

// FindApp 查找单个应用程序
func (l *LinuxAppSearcher) FindApp(ctx context.Context, appName string, options *SearchOptions) (*AppInfo, error) {
	result := l.SearchApps(ctx, appName, options)
	if result.Error != nil {
		return nil, result.Error
	}
	
	if len(result.Apps) == 0 {
		return nil, fmt.Errorf("未找到应用: %s", appName)
	}
	
	return result.Apps[0], nil
}

// GetInstalledApps 获取所有已安装的应用程序
func (l *LinuxAppSearcher) GetInstalledApps(ctx context.Context, options *SearchOptions) *SearchResult {
	return l.SearchApps(ctx, "", options)
}

// RefreshCache 刷新应用缓存
func (l *LinuxAppSearcher) RefreshCache(ctx context.Context) error {
	l.cacheMutex.Lock()
	defer l.cacheMutex.Unlock()
	
	l.cache = make(map[string]*AppInfo)
	l.cacheTime = time.Now()
	return nil
}

// GetCacheStats 获取缓存统计信息
func (l *LinuxAppSearcher) GetCacheStats() map[string]interface{} {
	l.cacheMutex.RLock()
	defer l.cacheMutex.RUnlock()
	
	return map[string]interface{}{
		"cache_size":  len(l.cache),
		"cache_time":  l.cacheTime,
		"platform":    "linux",
	}
}

// searchByDesktopFiles 通过.desktop文件搜索
func (l *LinuxAppSearcher) searchByDesktopFiles(ctx context.Context, query string, searchPaths []string) []*AppInfo {
	var apps []*AppInfo
	
	desktopPaths := []string{
		"/usr/share/applications",
		"/usr/local/share/applications",
		filepath.Join(os.Getenv("HOME"), ".local/share/applications"),
	}
	
	for _, desktopPath := range desktopPaths {
		if desktopApps := l.searchDesktopDirectory(desktopPath, query); len(desktopApps) > 0 {
			apps = append(apps, desktopApps...)
		}
	}
	
	return apps
}

// searchDesktopDirectory 搜索desktop文件目录
func (l *LinuxAppSearcher) searchDesktopDirectory(dirPath, query string) []*AppInfo {
	var apps []*AppInfo
	
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return apps
	}
	
	for _, entry := range entries {
		if strings.HasSuffix(entry.Name(), ".desktop") {
			fullPath := filepath.Join(dirPath, entry.Name())
			if app := l.parseDesktopFile(fullPath, query); app != nil {
				apps = append(apps, app)
			}
		}
	}
	
	return apps
}

// parseDesktopFile 解析.desktop文件
func (l *LinuxAppSearcher) parseDesktopFile(filePath, query string) *AppInfo {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil
	}
	
	lines := strings.Split(string(content), "\n")
	app := &AppInfo{}
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "Name=") {
			app.DisplayName = strings.TrimPrefix(line, "Name=")
			app.Name = app.DisplayName
		} else if strings.HasPrefix(line, "Exec=") {
			execLine := strings.TrimPrefix(line, "Exec=")
			// 解析Exec行，提取可执行文件路径
			app.ExecutablePath = l.parseExecLine(execLine)
		} else if strings.HasPrefix(line, "Icon=") {
			app.IconPath = strings.TrimPrefix(line, "Icon=")
		} else if strings.HasPrefix(line, "Comment=") {
			app.Description = strings.TrimPrefix(line, "Comment=")
		} else if strings.HasPrefix(line, "Categories=") {
			app.Category = strings.TrimPrefix(line, "Categories=")
		}
	}
	
	// 检查是否匹配查询
	if query != "" && !containsIgnoreCase(app.DisplayName, query) && 
	   !containsIgnoreCase(app.Name, query) && 
	   !containsIgnoreCase(app.Description, query) {
		return nil
	}
	
	return app
}

// parseExecLine 解析Exec行
func (l *LinuxAppSearcher) parseExecLine(execLine string) string {
	// 移除参数，只保留可执行文件路径
	parts := strings.Fields(execLine)
	if len(parts) > 0 {
		return parts[0]
	}
	return ""
}

// searchByPATH 通过PATH环境变量搜索
func (l *LinuxAppSearcher) searchByPATH(ctx context.Context, query string) []*AppInfo {
	var apps []*AppInfo
	
	pathEnv := os.Getenv("PATH")
	pathDirs := strings.Split(pathEnv, ":")
	
	for _, pathDir := range pathDirs {
		if pathApps := l.searchInDirectory(pathDir, query); len(pathApps) > 0 {
			apps = append(apps, pathApps...)
		}
	}
	
	return apps
}

// searchByPackageManager 通过包管理器搜索
func (l *LinuxAppSearcher) searchByPackageManager(ctx context.Context, query string) []*AppInfo {
	var apps []*AppInfo
	
	// 尝试不同的包管理器
	packageManagers := []struct {
		cmd  string
		args []string
	}{
		{"dpkg", []string{"-l", "*" + query + "*"}},           // Debian/Ubuntu
		{"rpm", []string{"-qa", "*" + query + "*"}},           // Red Hat/CentOS
		{"pacman", []string{"-Q", query}},                     // Arch Linux
		{"zypper", []string{"search", "--installed", query}}, // openSUSE
	}
	
	for _, pm := range packageManagers {
		if _, err := exec.LookPath(pm.cmd); err == nil {
			if pkgApps := l.searchWithPackageManager(ctx, pm.cmd, pm.args, query); len(pkgApps) > 0 {
				apps = append(apps, pkgApps...)
				break // 找到一个包管理器就够了
			}
		}
	}
	
	return apps
}

// searchWithPackageManager 使用特定包管理器搜索
func (l *LinuxAppSearcher) searchWithPackageManager(ctx context.Context, cmd string, args []string, query string) []*AppInfo {
	var apps []*AppInfo
	
	execCmd := exec.CommandContext(ctx, cmd, args...)
	output, err := execCmd.Output()
	if err != nil {
		return apps
	}
	
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(strings.ToLower(line), strings.ToLower(query)) {
			if app := l.parsePackageManagerLine(line, cmd); app != nil {
				apps = append(apps, app)
			}
		}
	}
	
	return apps
}

// parsePackageManagerLine 解析包管理器输出行
func (l *LinuxAppSearcher) parsePackageManagerLine(line, pmType string) *AppInfo {
	// 简化的包管理器输出解析
	// 实际项目中需要针对不同包管理器的具体格式解析
	fields := strings.Fields(line)
	if len(fields) >= 2 {
		return &AppInfo{
			Name:        fields[1],
			DisplayName: fields[1],
			Description: "通过" + pmType + "找到的包",
		}
	}
	return nil
}

// searchByFileSystem 文件系统搜索
func (l *LinuxAppSearcher) searchByFileSystem(ctx context.Context, query string, searchPaths []string) []*AppInfo {
	var apps []*AppInfo
	
	for _, searchPath := range searchPaths {
		if fsApps := l.searchInDirectory(searchPath, query); len(fsApps) > 0 {
			apps = append(apps, fsApps...)
		}
	}
	
	return apps
}

// searchInDirectory 在目录中搜索
func (l *LinuxAppSearcher) searchInDirectory(dirPath, query string) []*AppInfo {
	var apps []*AppInfo
	
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return apps
	}
	
	for _, entry := range entries {
		if query == "" || containsIgnoreCase(entry.Name(), query) {
			fullPath := filepath.Join(dirPath, entry.Name())
			
			// 检查是否是可执行文件
			if info, err := entry.Info(); err == nil && info.Mode()&0111 != 0 {
				app := &AppInfo{
					Name:           entry.Name(),
					DisplayName:    entry.Name(),
					ExecutablePath: fullPath,
					InstallPath:    dirPath,
				}
				apps = append(apps, app)
			}
		}
	}
	
	return apps
}

// 辅助方法
func (l *LinuxAppSearcher) deduplicateApps(apps []*AppInfo) []*AppInfo {
	seen := make(map[string]bool)
	var unique []*AppInfo
	
	for _, app := range apps {
		key := strings.ToLower(app.Name + "|" + app.ExecutablePath)
		if !seen[key] {
			seen[key] = true
			unique = append(unique, app)
		}
	}
	
	return unique
}

func (l *LinuxAppSearcher) sortAppsByConfidence(apps []*AppInfo) {
	// 简单的冒泡排序，按置信度降序
	for i := 0; i < len(apps)-1; i++ {
		for j := 0; j < len(apps)-i-1; j++ {
			if apps[j].Confidence < apps[j+1].Confidence {
				apps[j], apps[j+1] = apps[j+1], apps[j]
			}
		}
	}
}
