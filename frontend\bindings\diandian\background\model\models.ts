// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import { Create as $Create } from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as time$0 from "../../../time/models.js";

export class Message {
    "id": `${number}`;
    "created_at"?: number;
    "updated_at"?: number;
    "conversation_id"?: `${number}`;

    /**
     * user, assistant
     */
    "role"?: string;
    "content": string;

    /** Creates a new Message instance. */
    constructor($$source: Partial<Message> = {}) {
        if (!("id" in $$source)) {
            this["id"] = "0";
        }
        if (!("content" in $$source)) {
            this["content"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new Message instance from a string or object.
     */
    static createFrom($$source: any = {}): Message {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new Message($$parsedSource as Partial<Message>);
    }
}

export class Setting {
    "id": `${number}`;
    "created_at"?: number;
    "updated_at"?: number;
    "key"?: string;
    "value"?: string | null;
    "group_name"?: string;
    "name"?: string;
    "desc"?: string;
    "order_num"?: number;

    /**
     * 是否在设置界面展示
     */
    "showable"?: boolean | null;

    /**
     * 设置项类型，值为input/select/checkbox/switch等
     */
    "setting_type"?: string;

    /**
     * 可选值，仅在Type为select/checkbox时有效，格式为JSON数组
     */
    "options"?: string;

    /**
     * 占用列数，默认1，最大12
     */
    "cols"?: number;

    /** Creates a new Setting instance. */
    constructor($$source: Partial<Setting> = {}) {
        if (!("id" in $$source)) {
            this["id"] = "0";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new Setting instance from a string or object.
     */
    static createFrom($$source: any = {}): Setting {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new Setting($$parsedSource as Partial<Setting>);
    }
}

export class Step {
    "id": `${number}`;
    "created_at"?: number;
    "updated_at"?: number;
    "task_id": `${number}`;

    /**
     * 步骤序号
     */
    "step_index": number;

    /**
     * 展示给用户的消息内容
     */
    "content": string;

    /**
     * message, action, screenshot, analysis
     */
    "step_type": string;

    /**
     * pending, running, completed, failed, skipped
     */
    "status": string;

    /**
     * click, type, key, scroll, wait
     */
    "action_type": string;

    /**
     * 操作坐标 (x,y)
     */
    "coordinates": string;

    /**
     * 操作数据（如输入的文本、按键等）
     */
    "action_data": string;

    /**
     * 期望结果
     */
    "expected_outcome": string;

    /**
     * 截图文件路径
     */
    "screenshot": string;

    /**
     * 步骤执行结果
     */
    "result": string;

    /**
     * 错误信息
     */
    "error_msg": string;

    /**
     * 重试次数
     */
    "retry_count": number;

    /**
     * 最大重试次数
     */
    "max_retries": number;

    /**
     * 优先级 1-10
     */
    "priority": number;

    /**
     * 是否可选步骤
     */
    "optional": boolean;

    /**
     * 步骤开始时间
     */
    "start_time": time$0.Time | null;

    /**
     * 步骤结束时间
     */
    "end_time": time$0.Time | null;

    /**
     * 执行耗时(毫秒)
     */
    "duration": number;

    /**
     * 上下文信息
     */
    "context": string;

    /**
     * 元数据JSON
     */
    "metadata": string;

    /** Creates a new Step instance. */
    constructor($$source: Partial<Step> = {}) {
        if (!("id" in $$source)) {
            this["id"] = "0";
        }
        if (!("task_id" in $$source)) {
            this["task_id"] = "0";
        }
        if (!("step_index" in $$source)) {
            this["step_index"] = 0;
        }
        if (!("content" in $$source)) {
            this["content"] = "";
        }
        if (!("step_type" in $$source)) {
            this["step_type"] = "";
        }
        if (!("status" in $$source)) {
            this["status"] = "";
        }
        if (!("action_type" in $$source)) {
            this["action_type"] = "";
        }
        if (!("coordinates" in $$source)) {
            this["coordinates"] = "";
        }
        if (!("action_data" in $$source)) {
            this["action_data"] = "";
        }
        if (!("expected_outcome" in $$source)) {
            this["expected_outcome"] = "";
        }
        if (!("screenshot" in $$source)) {
            this["screenshot"] = "";
        }
        if (!("result" in $$source)) {
            this["result"] = "";
        }
        if (!("error_msg" in $$source)) {
            this["error_msg"] = "";
        }
        if (!("retry_count" in $$source)) {
            this["retry_count"] = 0;
        }
        if (!("max_retries" in $$source)) {
            this["max_retries"] = 0;
        }
        if (!("priority" in $$source)) {
            this["priority"] = 0;
        }
        if (!("optional" in $$source)) {
            this["optional"] = false;
        }
        if (!("start_time" in $$source)) {
            this["start_time"] = null;
        }
        if (!("end_time" in $$source)) {
            this["end_time"] = null;
        }
        if (!("duration" in $$source)) {
            this["duration"] = 0;
        }
        if (!("context" in $$source)) {
            this["context"] = "";
        }
        if (!("metadata" in $$source)) {
            this["metadata"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new Step instance from a string or object.
     */
    static createFrom($$source: any = {}): Step {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new Step($$parsedSource as Partial<Step>);
    }
}

export class Task {
    "id": `${number}`;
    "created_at"?: number;
    "updated_at"?: number;
    "conversation_id"?: `${number}`;
    "name": string;
    "description": string;

    /**
     * pending, running, completed, failed, cancelled
     */
    "status": string;

    /**
     * 0-100
     */
    "progress": number;

    /**
     * 1-10 任务优先级
     */
    "priority": number;

    /**
     * 任务开始时间
     */
    "start_time": time$0.Time | null;

    /**
     * 任务结束时间
     */
    "end_time": time$0.Time | null;

    /**
     * 任务执行结果
     */
    "result": string;

    /**
     * 最后的错误信息
     */
    "error_msg": string;

    /** Creates a new Task instance. */
    constructor($$source: Partial<Task> = {}) {
        if (!("id" in $$source)) {
            this["id"] = "0";
        }
        if (!("name" in $$source)) {
            this["name"] = "";
        }
        if (!("description" in $$source)) {
            this["description"] = "";
        }
        if (!("status" in $$source)) {
            this["status"] = "";
        }
        if (!("progress" in $$source)) {
            this["progress"] = 0;
        }
        if (!("priority" in $$source)) {
            this["priority"] = 0;
        }
        if (!("start_time" in $$source)) {
            this["start_time"] = null;
        }
        if (!("end_time" in $$source)) {
            this["end_time"] = null;
        }
        if (!("result" in $$source)) {
            this["result"] = "";
        }
        if (!("error_msg" in $$source)) {
            this["error_msg"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new Task instance from a string or object.
     */
    static createFrom($$source: any = {}): Task {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new Task($$parsedSource as Partial<Task>);
    }
}
