package appsearch

import (
	"context"
	"strings"
	"time"
)

// AppInfo 应用程序信息
type AppInfo struct {
	Name           string            `json:"name"`            // 应用名称
	DisplayName    string            `json:"display_name"`    // 显示名称
	ExecutablePath string            `json:"executable_path"` // 可执行文件路径
	InstallPath    string            `json:"install_path"`    // 安装路径
	Version        string            `json:"version"`         // 版本号
	Publisher      string            `json:"publisher"`       // 发布者
	Description    string            `json:"description"`     // 描述
	IconPath       string            `json:"icon_path"`       // 图标路径
	Category       string            `json:"category"`        // 分类
	Keywords       []string          `json:"keywords"`        // 关键词
	Metadata       map[string]string `json:"metadata"`        // 额外元数据
	Confidence     float64           `json:"confidence"`      // 匹配置信度 (0.0-1.0)
}

// SearchOptions 搜索选项
type SearchOptions struct {
	// 搜索策略
	UseRegistry       bool `json:"use_registry"`        // 使用注册表搜索
	UseFileSystem     bool `json:"use_filesystem"`      // 使用文件系统搜索
	UseSystemSearch   bool `json:"use_system_search"`   // 使用系统搜索API
	UsePackageManager bool `json:"use_package_manager"` // 使用包管理器

	// 搜索范围
	SearchPaths   []string `json:"search_paths"`   // 自定义搜索路径
	MaxDepth      int      `json:"max_depth"`      // 最大搜索深度
	IncludeHidden bool     `json:"include_hidden"` // 包含隐藏应用

	// 性能控制
	Timeout       time.Duration `json:"timeout"`        // 搜索超时
	MaxResults    int           `json:"max_results"`    // 最大结果数
	MinConfidence float64       `json:"min_confidence"` // 最小置信度

	// 过滤条件
	CategoryFilter  []string `json:"category_filter"`  // 分类过滤
	PublisherFilter []string `json:"publisher_filter"` // 发布者过滤
	ExcludePatterns []string `json:"exclude_patterns"` // 排除模式
}

// DefaultSearchOptions 默认搜索选项
func DefaultSearchOptions() *SearchOptions {
	return &SearchOptions{
		UseRegistry:       true,
		UseFileSystem:     true,
		UseSystemSearch:   true,
		UsePackageManager: true,
		MaxDepth:          3,
		IncludeHidden:     false,
		Timeout:           30 * time.Second,
		MaxResults:        50,
		MinConfidence:     0.1,
		SearchPaths:       []string{
			// 这些路径会在各平台实现中被覆盖
		},
	}
}

// SearchResult 搜索结果
type SearchResult struct {
	Apps     []*AppInfo    `json:"apps"`     // 找到的应用列表
	Duration time.Duration `json:"duration"` // 搜索耗时
	Strategy string        `json:"strategy"` // 使用的搜索策略
	Error    error         `json:"error"`    // 错误信息
}

// AppSearcher 应用搜索器接口
type AppSearcher interface {
	// SearchApps 搜索应用程序
	SearchApps(ctx context.Context, query string, options *SearchOptions) *SearchResult

	// FindApp 查找单个应用程序（返回最佳匹配）
	FindApp(ctx context.Context, appName string, options *SearchOptions) (*AppInfo, error)

	// GetInstalledApps 获取所有已安装的应用程序
	GetInstalledApps(ctx context.Context, options *SearchOptions) *SearchResult

	// RefreshCache 刷新应用缓存
	RefreshCache(ctx context.Context) error

	// GetCacheStats 获取缓存统计信息
	GetCacheStats() map[string]interface{}
}

// NewAppSearcher 创建平台特定的应用搜索器
func NewAppSearcher() AppSearcher {
	return newPlatformSearcher()
}

// SearchApps 便捷函数：搜索应用程序
func SearchApps(query string) *SearchResult {
	searcher := NewAppSearcher()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	return searcher.SearchApps(ctx, query, DefaultSearchOptions())
}

// FindApp 便捷函数：查找单个应用程序
func FindApp(appName string) (*AppInfo, error) {
	searcher := NewAppSearcher()
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	return searcher.FindApp(ctx, appName, DefaultSearchOptions())
}

// CalculateConfidence 计算匹配置信度
func CalculateConfidence(query string, app *AppInfo) float64 {
	query = normalizeString(query)
	confidence := 0.0

	// 名称完全匹配
	if normalizeString(app.Name) == query {
		confidence += 0.4
	}

	// 显示名称完全匹配
	if normalizeString(app.DisplayName) == query {
		confidence += 0.4
	}

	// 名称包含匹配
	if containsIgnoreCase(app.Name, query) {
		confidence += 0.2
	}

	// 显示名称包含匹配
	if containsIgnoreCase(app.DisplayName, query) {
		confidence += 0.2
	}

	// 关键词匹配
	for _, keyword := range app.Keywords {
		if containsIgnoreCase(keyword, query) {
			confidence += 0.1
		}
	}

	// 描述匹配
	if containsIgnoreCase(app.Description, query) {
		confidence += 0.05
	}

	// 确保置信度不超过1.0
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// 辅助函数
func normalizeString(s string) string {
	// 简单的字符串标准化，实际项目中可以使用更复杂的逻辑
	return strings.ToLower(strings.TrimSpace(s))
}

func containsIgnoreCase(s, substr string) bool {
	return strings.Contains(strings.ToLower(s), strings.ToLower(substr))
}
