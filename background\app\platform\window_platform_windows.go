//go:build windows

package platform

import (
	"github.com/wailsapp/wails/v3/pkg/application"
	"github.com/wailsapp/wails/v3/pkg/w32"
)

// WindowsPlatform Windows平台的窗口管理器
type WindowsPlatform struct{}

// newPlatformWindow 创建Windows平台窗口管理器
func newPlatformWindow() WindowPlatform {
	return &WindowsPlatform{}
}

// GetScreenSize 获取Windows屏幕尺寸
func (wp *WindowsPlatform) GetScreenSize() ScreenInfo {
	return ScreenInfo{
		Width:  int(w32.GetSystemMetrics(w32.SM_CXSCREEN)),
		Height: int(w32.GetSystemMetrics(w32.SM_CYSCREEN)),
	}
}

// GetPlatformWindowOptions 获取Windows平台特定的窗口选项
func (wp *WindowsPlatform) GetPlatformWindowOptions() interface{} {
	return application.WindowsWindow{
		// 可以根据需要设置Windows特定选项
		// ExStyle: w32.WS_EX_TOOLWINDOW,
	}
}

// GetFloatingWindowOptions 获取Windows浮动窗口的平台特定选项
func (wp *WindowsPlatform) GetFloatingWindowOptions() interface{} {
	return application.WindowsWindow{
		ExStyle:         w32.WS_EX_LAYERED | w32.WS_EX_TOOLWINDOW | w32.WS_EX_TOPMOST,
		HiddenOnTaskbar: true,
	}
}
