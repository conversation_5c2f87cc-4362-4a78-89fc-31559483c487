// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import { Call as $Call, CancellablePromise as $CancellablePromise, Create as $Create } from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as model$0 from "../model/models.js";

/**
 * 确认执行自动化任务
 */
export function ConfirmAutomationTask(t: model$0.Task | null, confirmed: boolean): $CancellablePromise<void> {
    return $Call.ByID(2394226322, t, confirmed);
}

/**
 * GetTaskSteps 获取任务的执行步骤
 */
export function GetTaskSteps(taskID: number): $CancellablePromise<(model$0.Step | null)[]> {
    return $Call.ByID(2223553812, taskID).then(($result: any) => {
        return $$createType2($result);
    });
}

/**
 * GetTaskWithSteps 获取任务及其执行步骤
 */
export function GetTaskWithSteps(taskID: number): $CancellablePromise<[model$0.Task | null, (model$0.Step | null)[]]> {
    return $Call.ByID(162262528, taskID).then(($result: any) => {
        $result[0] = $$createType4($result[0]);
        $result[1] = $$createType2($result[1]);
        return $result;
    });
}

/**
 * 处理新消息
 */
export function NewMessage(msg: model$0.Message | null): $CancellablePromise<void> {
    return $Call.ByID(3872484185, msg);
}

// Private type creation functions
const $$createType0 = model$0.Step.createFrom;
const $$createType1 = $Create.Nullable($$createType0);
const $$createType2 = $Create.Array($$createType1);
const $$createType3 = model$0.Task.createFrom;
const $$createType4 = $Create.Nullable($$createType3);
