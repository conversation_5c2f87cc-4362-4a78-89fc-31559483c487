package service

import (
	"context"
	"testing"
	"time"

	"diandian/background/domain"
)

// TestUnifiedExecutionEngine 测试统一执行引擎
func TestUnifiedExecutionEngine(t *testing.T) {
	// 创建模拟的AutomationService
	automationService := &AutomationService{}
	
	// 创建统一执行引擎
	engine := NewUnifiedExecutionEngine(automationService)
	
	if engine == nil {
		t.Fatal("创建统一执行引擎失败")
	}
	
	// 验证引擎的各个组件是否正确初始化
	if engine.automationService == nil {
		t.Error("automationService未正确初始化")
	}
	
	if engine.llmService == nil {
		t.Error("llmService未正确初始化")
	}
	
	if engine.visionService == nil {
		t.Error("visionService未正确初始化")
	}
	
	if engine.recoveryEngine == nil {
		t.Error("recoveryEngine未正确初始化")
	}
	
	if engine.lifecycleManager == nil {
		t.Error("lifecycleManager未正确初始化")
	}
}

// TestStepLifecycleManager 测试步骤生命周期管理器
func TestStepLifecycleManager(t *testing.T) {
	automationService := &AutomationService{}
	manager := NewStepLifecycleManager(automationService)
	
	if manager == nil {
		t.Fatal("创建步骤生命周期管理器失败")
	}
	
	if manager.automationService == nil {
		t.Error("automationService未正确初始化")
	}
}

// TestFallbackOperationValidation 测试保底操作验证
func TestFallbackOperationValidation(t *testing.T) {
	automationService := &AutomationService{}
	engine := NewUnifiedExecutionEngine(automationService)
	
	// 测试有效的点击操作
	validClickOp := &FallbackOperation{
		Type: "click",
		Parameters: map[string]interface{}{
			"x":      100.0,
			"y":      200.0,
			"button": "left",
		},
		Confidence: 0.8,
	}
	
	if err := engine.validateFallbackOperation(validClickOp); err != nil {
		t.Errorf("有效点击操作验证失败: %v", err)
	}
	
	// 测试无效的点击操作（坐标超出范围）
	invalidClickOp := &FallbackOperation{
		Type: "click",
		Parameters: map[string]interface{}{
			"x":      5000.0, // 超出范围
			"y":      200.0,
			"button": "left",
		},
		Confidence: 0.8,
	}
	
	if err := engine.validateFallbackOperation(invalidClickOp); err == nil {
		t.Error("无效点击操作应该验证失败")
	}
	
	// 测试有效的输入操作
	validTypeOp := &FallbackOperation{
		Type: "type",
		Parameters: map[string]interface{}{
			"text": "Hello World",
		},
		Confidence: 0.9,
	}
	
	if err := engine.validateFallbackOperation(validTypeOp); err != nil {
		t.Errorf("有效输入操作验证失败: %v", err)
	}
	
	// 测试包含危险内容的输入操作
	dangerousTypeOp := &FallbackOperation{
		Type: "type",
		Parameters: map[string]interface{}{
			"text": "rm -rf /",
		},
		Confidence: 0.9,
	}
	
	if err := engine.validateFallbackOperation(dangerousTypeOp); err == nil {
		t.Error("危险输入操作应该验证失败")
	}
}

// TestParameterAdjustment 测试参数调整
func TestParameterAdjustment(t *testing.T) {
	automationService := &AutomationService{}
	engine := NewUnifiedExecutionEngine(automationService)
	
	// 测试点击参数调整
	clickPlan := &domain.AutomationStepPlan{
		Type:        "click",
		Description: "点击按钮",
		Context:     `{"x": 100, "y": 200}`,
	}
	
	adjustedPlan := engine.adjustStepParameters(clickPlan, "坐标错误")
	if adjustedPlan == nil {
		t.Error("点击参数调整失败")
	}
	
	// 测试等待参数调整
	waitPlan := &domain.AutomationStepPlan{
		Type:        "wait",
		Description: "等待",
		Context:     `{"duration": 1000}`,
	}
	
	adjustedWaitPlan := engine.adjustStepParameters(waitPlan, "超时")
	if adjustedWaitPlan == nil {
		t.Error("等待参数调整失败")
	}
}

// TestContextExtraction 测试上下文提取
func TestContextExtraction(t *testing.T) {
	automationService := &AutomationService{}
	engine := NewUnifiedExecutionEngine(automationService)
	
	// 测试文本提取
	jsonContext := `{"text": "Hello World"}`
	text := engine.extractTextFromContext(jsonContext)
	if text != "Hello World" {
		t.Errorf("文本提取失败，期望: Hello World, 实际: %s", text)
	}
	
	// 测试应用名提取
	appContext := `{"app": "notepad"}`
	app := engine.extractAppNameFromContext(appContext)
	if app != "notepad" {
		t.Errorf("应用名提取失败，期望: notepad, 实际: %s", app)
	}
	
	// 测试持续时间提取
	durationContext := `{"duration": 2000}`
	duration := engine.extractDurationFromContext(durationContext)
	if duration != 2000 {
		t.Errorf("持续时间提取失败，期望: 2000, 实际: %d", duration)
	}
	
	// 测试按键提取
	keyContext := `{"key": "Return", "modifiers": ["ctrl"]}`
	key, modifiers := engine.extractKeyFromContext(keyContext)
	if key != "Return" {
		t.Errorf("按键提取失败，期望: Return, 实际: %s", key)
	}
	if len(modifiers) != 1 || modifiers[0] != "ctrl" {
		t.Errorf("修饰键提取失败，期望: [ctrl], 实际: %v", modifiers)
	}
}

// TestTaskRecoveryEngine 测试任务恢复引擎
func TestTaskRecoveryEngine(t *testing.T) {
	automationService := &AutomationService{}
	recoveryEngine := NewTaskRecoveryEngine(automationService)
	
	if recoveryEngine == nil {
		t.Fatal("创建任务恢复引擎失败")
	}
	
	if recoveryEngine.automationService == nil {
		t.Error("automationService未正确初始化")
	}
	
	if recoveryEngine.llmService == nil {
		t.Error("llmService未正确初始化")
	}
	
	if recoveryEngine.visionService == nil {
		t.Error("visionService未正确初始化")
	}
}

// TestSuggestionParsing 测试建议解析
func TestSuggestionParsing(t *testing.T) {
	automationService := &AutomationService{}
	recoveryEngine := NewTaskRecoveryEngine(automationService)
	
	// 模拟原始步骤
	originalStep := &model.Step{
		ActionType:      "click",
		ActionData:      `{"target": "button"}`,
		ExpectedOutcome: "点击按钮",
		Optional:        false,
	}
	
	// 测试点击建议解析
	clickSuggestion := "点击屏幕中央的按钮"
	stepPlan := recoveryEngine.parseSuggestionToStepPlan(clickSuggestion, originalStep)
	if stepPlan == nil {
		t.Error("点击建议解析失败")
	} else if stepPlan.Type != "click" {
		t.Errorf("点击建议类型错误，期望: click, 实际: %s", stepPlan.Type)
	}
	
	// 测试输入建议解析
	typeSuggestion := "输入用户名"
	stepPlan = recoveryEngine.parseSuggestionToStepPlan(typeSuggestion, originalStep)
	if stepPlan == nil {
		t.Error("输入建议解析失败")
	} else if stepPlan.Type != "type" {
		t.Errorf("输入建议类型错误，期望: type, 实际: %s", stepPlan.Type)
	}
	
	// 测试按键建议解析
	keySuggestion := "按键Enter确认"
	stepPlan = recoveryEngine.parseSuggestionToStepPlan(keySuggestion, originalStep)
	if stepPlan == nil {
		t.Error("按键建议解析失败")
	} else if stepPlan.Type != "key_press" {
		t.Errorf("按键建议类型错误，期望: key_press, 实际: %s", stepPlan.Type)
	}
}

// BenchmarkStepExecution 性能测试
func BenchmarkStepExecution(b *testing.B) {
	automationService := &AutomationService{}
	engine := NewUnifiedExecutionEngine(automationService)
	
	stepPlan := &domain.AutomationStepPlan{
		Type:        "wait",
		Description: "等待测试",
		Context:     `{"duration": 1}`,
	}
	
	ctx := context.Background()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		engine.executeStepPlan(ctx, stepPlan)
	}
}
