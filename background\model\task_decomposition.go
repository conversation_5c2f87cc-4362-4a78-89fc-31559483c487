package model

import (
	"encoding/json"
	"time"
)

// TaskDecomposition 任务分解信息
type TaskDecomposition struct {
	Base
	TaskID            uint64     `json:"task_id,string" gorm:"index;not null"`
	TaskType          string     `json:"task_type" gorm:"size:50"`            // simple, medium, complex
	Description       string     `json:"description" gorm:"type:text"`        // 任务描述
	ExpectedOutcome   string     `json:"expected_outcome" gorm:"type:text"`   // 预期结果
	RiskLevel         string     `json:"risk_level" gorm:"size:20"`           // low, medium, high
	EstimatedTime     int        `json:"estimated_time"`                      // 预估时间(秒)
	OriginalAnalysis  string     `json:"original_analysis" gorm:"type:text"`  // 原始大模型分析JSON
	DecompositionData string     `json:"decomposition_data" gorm:"type:text"` // 分解数据JSON
	Status            string     `json:"status" gorm:"size:20"`               // pending, active, completed, failed
	StartTime         *time.Time `json:"start_time"`                          // 开始执行时间
	EndTime           *time.Time `json:"end_time"`                            // 结束时间

	// 注意：关联关系通过外键建立，避免循环引用
}

// TaskDecomposition状态常量
const (
	TaskDecompositionStatusPending   = "pending"
	TaskDecompositionStatusActive    = "active"
	TaskDecompositionStatusCompleted = "completed"
	TaskDecompositionStatusFailed    = "failed"
)

// GetDecompositionData 获取分解数据
func (td *TaskDecomposition) GetDecompositionData() (map[string]interface{}, error) {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(td.DecompositionData), &data); err != nil {
		return nil, err
	}
	return data, nil
}

// SetDecompositionData 设置分解数据
func (td *TaskDecomposition) SetDecompositionData(data interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	td.DecompositionData = string(jsonData)
	return nil
}

// GetOriginalAnalysis 获取原始分析数据
func (td *TaskDecomposition) GetOriginalAnalysis() (map[string]interface{}, error) {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(td.OriginalAnalysis), &data); err != nil {
		return nil, err
	}
	return data, nil
}

// SetOriginalAnalysis 设置原始分析数据
func (td *TaskDecomposition) SetOriginalAnalysis(data interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	td.OriginalAnalysis = string(jsonData)
	return nil
}
