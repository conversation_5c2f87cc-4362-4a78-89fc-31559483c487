package model

import (
	"encoding/json"
	"time"
)

// StepEvaluation 步骤评估结果
type StepEvaluation struct {
	Base
	StepID           uint64    `json:"step_id,string" gorm:"index;not null"`
	TaskID           uint64    `json:"task_id,string" gorm:"index;not null"`
	EvaluationType   string    `json:"evaluation_type" gorm:"size:50"`   // auto, manual, visual
	EvaluationResult string    `json:"evaluation_result" gorm:"size:20"` // success, failure, partial, unknown
	Confidence       float64   `json:"confidence"`                       // 0.0-1.0 评估置信度
	FailureReason    string    `json:"failure_reason" gorm:"type:text"`  // 失败原因
	ExpectedState    string    `json:"expected_state" gorm:"type:text"`  // 期望状态描述
	ActualState      string    `json:"actual_state" gorm:"type:text"`    // 实际状态描述
	ScreenshotPath   string    `json:"screenshot_path" gorm:"size:500"`  // 评估时的截图路径
	EvaluationData   string    `json:"evaluation_data" gorm:"type:text"` // 评估详细数据JSON
	RecoveryStrategy string    `json:"recovery_strategy" gorm:"size:50"` // 恢复策略
	EvaluatedAt      time.Time `json:"evaluated_at"`                     // 评估时间

	// 注意：关联关系通过外键建立，避免循环引用
}

// 评估类型常量
const (
	EvaluationTypeAuto   = "auto"   // 自动评估
	EvaluationTypeManual = "manual" // 手动评估
	EvaluationTypeVisual = "visual" // 视觉分析评估
)

// 评估结果常量
const (
	EvaluationResultSuccess = "success" // 成功
	EvaluationResultFailure = "failure" // 失败
	EvaluationResultPartial = "partial" // 部分成功
	EvaluationResultUnknown = "unknown" // 未知状态
)

// 恢复策略常量
const (
	RecoveryStrategyRetry      = "retry"      // 重试当前步骤
	RecoveryStrategySkip       = "skip"       // 跳过当前步骤
	RecoveryStrategyReplan     = "replan"     // 重新规划后续步骤
	RecoveryStrategyScreenshot = "screenshot" // 截图分析
	RecoveryStrategyManual     = "manual"     // 需要人工干预
	RecoveryStrategyAbort      = "abort"      // 终止任务
)

// GetEvaluationData 获取评估数据
func (se *StepEvaluation) GetEvaluationData() (map[string]interface{}, error) {
	if se.EvaluationData == "" {
		return make(map[string]interface{}), nil
	}
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(se.EvaluationData), &data); err != nil {
		return nil, err
	}
	return data, nil
}

// SetEvaluationData 设置评估数据
func (se *StepEvaluation) SetEvaluationData(data interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	se.EvaluationData = string(jsonData)
	return nil
}

// IsSuccess 判断是否评估成功
func (se *StepEvaluation) IsSuccess() bool {
	return se.EvaluationResult == EvaluationResultSuccess
}

// IsFailure 判断是否评估失败
func (se *StepEvaluation) IsFailure() bool {
	return se.EvaluationResult == EvaluationResultFailure
}

// NeedsRecovery 判断是否需要恢复处理
func (se *StepEvaluation) NeedsRecovery() bool {
	return se.EvaluationResult == EvaluationResultFailure ||
		se.EvaluationResult == EvaluationResultPartial ||
		se.EvaluationResult == EvaluationResultUnknown
}
