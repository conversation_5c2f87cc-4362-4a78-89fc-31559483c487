//go:build linux

package platform

import (
	"os/exec"
	"strconv"
	"strings"
)

// LinuxPlatform Linux平台的窗口管理器
type LinuxPlatform struct{}

// newPlatformWindow 创建Linux平台窗口管理器
func newPlatformWindow() WindowPlatform {
	return &LinuxPlatform{}
}

// GetScreenSize 获取Linux屏幕尺寸
func (lp *LinuxPlatform) GetScreenSize() ScreenInfo {
	// 尝试多种方法获取屏幕尺寸

	// 方法1: 使用xrandr
	if screenInfo := lp.getScreenSizeWithXrandr(); screenInfo.Width > 0 {
		return screenInfo
	}

	// 方法2: 使用xdpyinfo
	if screenInfo := lp.getScreenSizeWithXdpyinfo(); screenInfo.Width > 0 {
		return screenInfo
	}

	// 方法3: 使用环境变量或默认值
	return lp.getDefaultScreenSize()
}

// getScreenSizeWithXrandr 使用xrandr获取屏幕尺寸
func (lp *LinuxPlatform) getScreenSizeWithXrandr() ScreenInfo {
	cmd := exec.Command("xrandr")
	output, err := cmd.Output()
	if err != nil {
		return ScreenInfo{}
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, " connected ") && strings.Contains(line, "primary") {
			// 解析主显示器信息
			if width, height := lp.parseXrandrLine(line); width > 0 && height > 0 {
				return ScreenInfo{Width: width, Height: height}
			}
		}
	}

	// 如果没有找到primary，查找第一个connected的显示器
	for _, line := range lines {
		if strings.Contains(line, " connected ") {
			if width, height := lp.parseXrandrLine(line); width > 0 && height > 0 {
				return ScreenInfo{Width: width, Height: height}
			}
		}
	}

	return ScreenInfo{}
}

// parseXrandrLine 解析xrandr输出行
func (lp *LinuxPlatform) parseXrandrLine(line string) (int, int) {
	// 寻找类似 "1920x1080+0+0" 的模式
	parts := strings.Fields(line)
	for _, part := range parts {
		if strings.Contains(part, "x") && strings.Contains(part, "+") {
			// 提取分辨率部分
			resPart := strings.Split(part, "+")[0]
			if strings.Contains(resPart, "x") {
				dimensions := strings.Split(resPart, "x")
				if len(dimensions) == 2 {
					if width, err := strconv.Atoi(dimensions[0]); err == nil {
						if height, err := strconv.Atoi(dimensions[1]); err == nil {
							return width, height
						}
					}
				}
			}
		}
	}
	return 0, 0
}

// getScreenSizeWithXdpyinfo 使用xdpyinfo获取屏幕尺寸
func (lp *LinuxPlatform) getScreenSizeWithXdpyinfo() ScreenInfo {
	cmd := exec.Command("xdpyinfo")
	output, err := cmd.Output()
	if err != nil {
		return ScreenInfo{}
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "dimensions:") {
			// 解析类似 "dimensions:    1920x1080 pixels (508x285 millimeters)" 的行
			parts := strings.Fields(line)
			if len(parts) >= 2 {
				dimensions := parts[1]
				if strings.Contains(dimensions, "x") {
					dimParts := strings.Split(dimensions, "x")
					if len(dimParts) == 2 {
						if width, err := strconv.Atoi(dimParts[0]); err == nil {
							if height, err := strconv.Atoi(dimParts[1]); err == nil {
								return ScreenInfo{Width: width, Height: height}
							}
						}
					}
				}
			}
		}
	}

	return ScreenInfo{}
}

// getDefaultScreenSize 获取默认屏幕尺寸
func (lp *LinuxPlatform) getDefaultScreenSize() ScreenInfo {
	// 可以尝试从环境变量获取，或使用常见的默认值
	return ScreenInfo{Width: 1920, Height: 1080}
}

// GetPlatformWindowOptions 获取Linux平台特定的窗口选项
func (lp *LinuxPlatform) GetPlatformWindowOptions() interface{} {
	// Linux平台通常不需要特殊的窗口选项
	// 返回空的结构体或nil
	return struct{}{}
}

// GetFloatingWindowOptions 获取Linux浮动窗口的平台特定选项
func (lp *LinuxPlatform) GetFloatingWindowOptions() interface{} {
	// Linux平台的浮动窗口选项
	// 可以根据不同的窗口管理器进行调整
	return struct{}{}
}
