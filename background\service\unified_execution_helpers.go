package service

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"strconv"
	"strings"
	"time"

	"diandian/background/domain"
)

// 上下文解析方法

// extractTextFromContext 从上下文中提取文本
func (e *UnifiedExecutionEngine) extractTextFromContext(context string) string {
	// 尝试解析JSON格式
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(context), &data); err == nil {
		if text, ok := data["text"].(string); ok {
			return text
		}
		if input, ok := data["input"].(string); ok {
			return input
		}
		if content, ok := data["content"].(string); ok {
			return content
		}
	}

	// 如果不是JSON，尝试直接提取
	if strings.Contains(context, "输入") || strings.Contains(context, "文本") {
		// 简单的文本提取逻辑
		parts := strings.Split(context, ":")
		if len(parts) > 1 {
			return strings.TrimSpace(parts[1])
		}
	}

	return context
}

// extractAppNameFromContext 从上下文中提取应用名称
func (e *UnifiedExecutionEngine) extractAppNameFromContext(context string) string {
	// 尝试解析JSON格式
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(context), &data); err == nil {
		if app, ok := data["app"].(string); ok {
			return app
		}
		if name, ok := data["name"].(string); ok {
			return name
		}
		if application, ok := data["application"].(string); ok {
			return application
		}
	}

	// 如果不是JSON，尝试直接提取
	if strings.Contains(context, "启动") || strings.Contains(context, "打开") {
		// 简单的应用名提取逻辑
		parts := strings.Split(context, " ")
		for _, part := range parts {
			if part != "启动" && part != "打开" && part != "" {
				return part
			}
		}
	}

	return context
}

// extractPathFromContext 从上下文中提取路径
func (e *UnifiedExecutionEngine) extractPathFromContext(context string) string {
	// 尝试解析JSON格式
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(context), &data); err == nil {
		if path, ok := data["path"].(string); ok {
			return path
		}
		if file, ok := data["file"].(string); ok {
			return file
		}
	}

	// 如果不是JSON，尝试直接提取
	if strings.Contains(context, ".png") || strings.Contains(context, ".jpg") || strings.Contains(context, ".jpeg") {
		return context
	}

	return ""
}

// extractDurationFromContext 从上下文中提取持续时间
func (e *UnifiedExecutionEngine) extractDurationFromContext(context string) int {
	// 尝试解析JSON格式
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(context), &data); err == nil {
		if duration, ok := data["duration"].(float64); ok {
			return int(duration)
		}
		if wait, ok := data["wait"].(float64); ok {
			return int(wait)
		}
		if time, ok := data["time"].(float64); ok {
			return int(time)
		}
	}

	// 如果不是JSON，尝试提取数字
	if strings.Contains(context, "秒") {
		parts := strings.Split(context, "秒")
		if len(parts) > 0 {
			if num, err := strconv.Atoi(strings.TrimSpace(parts[0])); err == nil {
				return num * 1000 // 转换为毫秒
			}
		}
	}

	if strings.Contains(context, "毫秒") {
		parts := strings.Split(context, "毫秒")
		if len(parts) > 0 {
			if num, err := strconv.Atoi(strings.TrimSpace(parts[0])); err == nil {
				return num
			}
		}
	}

	return 0
}

// extractKeyFromContext 从上下文中提取按键信息
func (e *UnifiedExecutionEngine) extractKeyFromContext(context string) (string, []string) {
	// 尝试解析JSON格式
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(context), &data); err == nil {
		key, _ := data["key"].(string)
		var modifiers []string
		if mods, ok := data["modifiers"].([]interface{}); ok {
			for _, mod := range mods {
				if modStr, ok := mod.(string); ok {
					modifiers = append(modifiers, modStr)
				}
			}
		}
		return key, modifiers
	}

	// 如果不是JSON，尝试直接解析
	if strings.Contains(context, "+") {
		parts := strings.Split(context, "+")
		if len(parts) > 1 {
			modifiers := parts[:len(parts)-1]
			key := parts[len(parts)-1]
			return strings.TrimSpace(key), modifiers
		}
	}

	return context, nil
}

// extractFileOperationFromContext 从上下文中提取文件操作信息
func (e *UnifiedExecutionEngine) extractFileOperationFromContext(context string) (string, string) {
	// 尝试解析JSON格式
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(context), &data); err == nil {
		operation, _ := data["operation"].(string)
		path, _ := data["path"].(string)
		return operation, path
	}

	// 如果不是JSON，尝试直接解析
	if strings.Contains(context, "打开") {
		return "open", strings.TrimSpace(strings.Replace(context, "打开", "", 1))
	}
	if strings.Contains(context, "创建") {
		return "create", strings.TrimSpace(strings.Replace(context, "创建", "", 1))
	}
	if strings.Contains(context, "删除") {
		return "delete", strings.TrimSpace(strings.Replace(context, "删除", "", 1))
	}

	return "", ""
}

// isGetClipboardOperation 判断是否为获取剪贴板操作
func (e *UnifiedExecutionEngine) isGetClipboardOperation(context string) bool {
	return strings.Contains(context, "获取") || strings.Contains(context, "读取") || strings.Contains(context, "get")
}

// 退化机制相关方法

// adjustStepParameters 调整步骤参数
func (e *UnifiedExecutionEngine) adjustStepParameters(stepPlan *domain.AutomationStepPlan, originalError string) *domain.AutomationStepPlan {
	adjustedPlan := *stepPlan // 复制原计划

	switch stepPlan.Type {
	case "click":
		return e.adjustClickParameters(&adjustedPlan, originalError)
	case "type":
		return e.adjustTypeParameters(&adjustedPlan, originalError)
	case "wait":
		return e.adjustWaitParameters(&adjustedPlan, originalError)
	default:
		return nil
	}
}

// adjustClickParameters 调整点击参数
func (e *UnifiedExecutionEngine) adjustClickParameters(stepPlan *domain.AutomationStepPlan, originalError string) *domain.AutomationStepPlan {
	// 如果是坐标相关错误，尝试调整坐标
	if strings.Contains(originalError, "坐标") || strings.Contains(originalError, "位置") {
		// 在原有上下文中添加坐标偏移信息
		var data map[string]interface{}
		if err := json.Unmarshal([]byte(stepPlan.Context), &data); err == nil {
			data["coordinate_offset"] = map[string]int{"x": 5, "y": 5}
			if newContext, err := json.Marshal(data); err == nil {
				stepPlan.Context = string(newContext)
				return stepPlan
			}
		}
	}

	// 如果是点击失败，尝试双击
	if strings.Contains(originalError, "点击失败") {
		var data map[string]interface{}
		if err := json.Unmarshal([]byte(stepPlan.Context), &data); err == nil {
			data["click_type"] = "double"
			if newContext, err := json.Marshal(data); err == nil {
				stepPlan.Context = string(newContext)
				return stepPlan
			}
		}
	}

	return nil
}

// adjustTypeParameters 调整输入参数
func (e *UnifiedExecutionEngine) adjustTypeParameters(stepPlan *domain.AutomationStepPlan, originalError string) *domain.AutomationStepPlan {
	// 如果是输入失败，尝试添加延迟
	if strings.Contains(originalError, "输入失败") {
		var data map[string]interface{}
		if err := json.Unmarshal([]byte(stepPlan.Context), &data); err == nil {
			data["typing_delay"] = 100 // 100ms延迟
			if newContext, err := json.Marshal(data); err == nil {
				stepPlan.Context = string(newContext)
				return stepPlan
			}
		}
	}

	return nil
}

// adjustWaitParameters 调整等待参数
func (e *UnifiedExecutionEngine) adjustWaitParameters(stepPlan *domain.AutomationStepPlan, originalError string) *domain.AutomationStepPlan {
	// 如果是等待时间不够，增加等待时间
	if strings.Contains(originalError, "超时") || strings.Contains(originalError, "等待") {
		duration := e.extractDurationFromContext(stepPlan.Context)
		if duration > 0 {
			newDuration := duration * 2 // 双倍等待时间
			var data map[string]interface{}
			if err := json.Unmarshal([]byte(stepPlan.Context), &data); err == nil {
				data["duration"] = newDuration
				if newContext, err := json.Marshal(data); err == nil {
					stepPlan.Context = string(newContext)
					return stepPlan
				}
			}
		}
	}

	return nil
}

// executeKeyboardAlternative 执行键盘替代方案
func (e *UnifiedExecutionEngine) executeKeyboardAlternative(stepPlan *domain.AutomationStepPlan) *domain.StepExecutionResult {
	slog.Info("尝试键盘替代方案", "original_type", stepPlan.Type)

	// 对于点击操作，尝试使用Tab键导航+Enter键确认
	result := &domain.StepExecutionResult{
		StepType: "keyboard_alternative",
		Success:  false,
	}

	// 先按Tab键几次尝试导航到目标元素
	for i := 0; i < 5; i++ {
		tabStep := AutomationStep{
			Type: "key",
			Parameters: map[string]interface{}{
				"key": "Tab",
			},
		}
		e.automationService.ExecuteStep(tabStep)
		time.Sleep(200 * time.Millisecond)
	}

	// 然后按Enter键确认
	enterStep := AutomationStep{
		Type: "key",
		Parameters: map[string]interface{}{
			"key": "Return",
		},
	}

	opResult := e.automationService.ExecuteStep(enterStep)
	if opResult.Success {
		result.Success = true
		result.Message = "键盘替代方案执行成功"
	} else {
		result.Error = fmt.Sprintf("键盘替代方案失败: %s", opResult.Error)
	}

	return result
}

// executeClipboardTypeAlternative 执行剪贴板输入替代方案
func (e *UnifiedExecutionEngine) executeClipboardTypeAlternative(stepPlan *domain.AutomationStepPlan) *domain.StepExecutionResult {
	slog.Info("尝试剪贴板输入替代方案", "original_type", stepPlan.Type)

	result := &domain.StepExecutionResult{
		StepType: "clipboard_alternative",
		Success:  false,
	}

	text := e.extractTextFromContext(stepPlan.Context)
	if text == "" {
		result.Error = "无法提取输入文本"
		return result
	}

	// 先设置剪贴板
	clipboardStep := AutomationStep{
		Type: "clipboard",
		Parameters: map[string]interface{}{
			"operation": "set",
			"text":      text,
		},
	}

	opResult := e.automationService.ExecuteStep(clipboardStep)
	if !opResult.Success {
		result.Error = fmt.Sprintf("设置剪贴板失败: %s", opResult.Error)
		return result
	}

	// 然后使用Ctrl+V粘贴
	pasteStep := AutomationStep{
		Type: "key",
		Parameters: map[string]interface{}{
			"key":       "v",
			"modifiers": []string{"ctrl"},
		},
	}

	opResult = e.automationService.ExecuteStep(pasteStep)
	if opResult.Success {
		result.Success = true
		result.Message = "剪贴板输入替代方案执行成功"
	} else {
		result.Error = fmt.Sprintf("剪贴板输入替代方案失败: %s", opResult.Error)
	}

	return result
}
