package service

import (
	"fmt"
	"log/slog"
	"strconv"
	"strings"
	"time"

	"diandian/background/domain"
)

// executeClickStep 执行点击步骤
func (e *UnifiedExecutionEngine) executeClickStep(stepPlan *domain.AutomationStepPlan, screenAnalysis *domain.VisualAnalysisResponse) *domain.StepExecutionResult {
	result := &domain.StepExecutionResult{
		StepType: stepPlan.Type,
		Success:  false,
	}

	// 使用LLM生成点击操作
	clickOp, err := e.llmService.GenerateClickOperation(stepPlan.Context, screenAnalysis)
	if err != nil {
		result.Error = fmt.Sprintf("生成点击操作失败: %v", err)
		return result
	}

	slog.Info("生成点击操作",
		"x", clickOp.X,
		"y", clickOp.Y,
		"button", clickOp.Button)

	// 通过AutomationService执行点击操作
	step := AutomationStep{
		Type: "click",
		Parameters: map[string]interface{}{
			"x":      float64(clickOp.X),
			"y":      float64(clickOp.Y),
			"button": clickOp.Button,
		},
	}

	opResult := e.automationService.ExecuteStep(step)
	if !opResult.Success {
		result.Error = fmt.Sprintf("点击操作失败: %s", opResult.Error)
		return result
	}

	result.Success = true
	result.Message = fmt.Sprintf("成功点击坐标 (%d, %d)", clickOp.X, clickOp.Y)
	result.Data = map[string]interface{}{
		"x":      clickOp.X,
		"y":      clickOp.Y,
		"button": clickOp.Button,
	}
	return result
}

// executeTypeStep 执行输入步骤
func (e *UnifiedExecutionEngine) executeTypeStep(stepPlan *domain.AutomationStepPlan) *domain.StepExecutionResult {
	result := &domain.StepExecutionResult{
		StepType: stepPlan.Type,
		Success:  false,
	}

	text := e.extractTextFromContext(stepPlan.Context)
	if text == "" {
		result.Error = "无法从上下文中提取输入文本"
		return result
	}

	step := AutomationStep{
		Type: "type",
		Parameters: map[string]interface{}{
			"text": text,
		},
	}

	opResult := e.automationService.ExecuteStep(step)
	if !opResult.Success {
		result.Error = fmt.Sprintf("输入操作失败: %s", opResult.Error)
		return result
	}

	result.Success = true
	result.Message = fmt.Sprintf("成功输入文本: %s", text)
	result.Data = map[string]interface{}{
		"text": text,
	}
	return result
}

// executeLaunchAppStep 执行启动应用步骤
func (e *UnifiedExecutionEngine) executeLaunchAppStep(stepPlan *domain.AutomationStepPlan) *domain.StepExecutionResult {
	result := &domain.StepExecutionResult{
		StepType: stepPlan.Type,
		Success:  false,
	}

	appIdentifier := e.extractAppNameFromContext(stepPlan.Context)
	if appIdentifier == "" {
		result.Error = "无法从上下文中提取应用名称"
		return result
	}

	// 使用智能应用启动器
	appLauncher := NewAppLauncher()
	err := appLauncher.LaunchApp(appIdentifier)
	if err != nil {
		result.Error = fmt.Sprintf("智能启动应用失败: %s", err.Error())

		// 如果智能启动失败，回退到原始方法
		step := AutomationStep{
			Type: "launch",
			Parameters: map[string]interface{}{
				"app": appIdentifier,
			},
		}

		opResult := e.automationService.ExecuteStep(step)
		if !opResult.Success {
			result.Error = fmt.Sprintf("启动应用失败 (智能启动和原始方法都失败): %s", opResult.Error)
			return result
		}
	}

	result.Success = true
	result.Message = fmt.Sprintf("成功启动应用: %s", appIdentifier)
	result.Data = map[string]interface{}{
		"app": appIdentifier,
	}
	return result
}

// executeFileStep 执行文件操作步骤
func (e *UnifiedExecutionEngine) executeFileStep(stepPlan *domain.AutomationStepPlan) *domain.StepExecutionResult {
	result := &domain.StepExecutionResult{
		StepType: stepPlan.Type,
		Success:  false,
	}

	operation, path := e.extractFileOperationFromContext(stepPlan.Context)
	if operation == "" || path == "" {
		result.Error = "无法从上下文中提取文件操作信息"
		return result
	}

	step := AutomationStep{
		Type: "file",
		Parameters: map[string]interface{}{
			"operation": operation,
			"path":      path,
		},
	}

	opResult := e.automationService.ExecuteStep(step)
	if !opResult.Success {
		result.Error = fmt.Sprintf("文件操作失败: %s", opResult.Error)
		return result
	}

	result.Success = true
	result.Message = fmt.Sprintf("成功执行文件操作: %s %s", operation, path)
	result.Data = map[string]interface{}{
		"operation": operation,
		"path":      path,
	}
	return result
}

// executeScreenshotStep 执行截屏步骤
func (e *UnifiedExecutionEngine) executeScreenshotStep(stepPlan *domain.AutomationStepPlan) *domain.StepExecutionResult {
	result := &domain.StepExecutionResult{
		StepType: stepPlan.Type,
		Success:  false,
	}

	path := e.extractPathFromContext(stepPlan.Context)
	if path == "" {
		path = "screenshot.png" // 默认路径
	}

	step := AutomationStep{
		Type: "screenshot",
		Parameters: map[string]interface{}{
			"path": path,
		},
	}

	opResult := e.automationService.ExecuteStep(step)
	if !opResult.Success {
		result.Error = fmt.Sprintf("截屏失败: %s", opResult.Error)
		return result
	}

	result.Success = true
	result.Message = fmt.Sprintf("成功截屏: %s", path)
	result.ScreenshotPath = path
	result.Data = map[string]interface{}{
		"path": path,
	}
	return result
}

// executeClipboardStep 执行剪贴板步骤
func (e *UnifiedExecutionEngine) executeClipboardStep(stepPlan *domain.AutomationStepPlan) *domain.StepExecutionResult {
	result := &domain.StepExecutionResult{
		StepType: stepPlan.Type,
		Success:  false,
	}

	var operation string
	var text string

	if e.isGetClipboardOperation(stepPlan.Context) {
		operation = "get"
	} else {
		operation = "set"
		text = e.extractTextFromContext(stepPlan.Context)
		if text == "" {
			result.Error = "无法从上下文中提取文本内容"
			return result
		}
	}

	step := AutomationStep{
		Type: "clipboard",
		Parameters: map[string]interface{}{
			"operation": operation,
			"text":      text,
		},
	}

	opResult := e.automationService.ExecuteStep(step)
	if !opResult.Success {
		result.Error = fmt.Sprintf("剪贴板操作失败: %s", opResult.Error)
		return result
	}

	result.Success = true
	if operation == "get" {
		result.Message = "获取剪贴板内容成功"
	} else {
		result.Message = fmt.Sprintf("设置剪贴板内容: %s", text)
	}
	result.Data = map[string]interface{}{
		"operation": operation,
		"text":      text,
	}
	return result
}

// executeWaitStep 执行等待步骤
func (e *UnifiedExecutionEngine) executeWaitStep(stepPlan *domain.AutomationStepPlan) *domain.StepExecutionResult {
	result := &domain.StepExecutionResult{
		StepType: stepPlan.Type,
		Success:  false,
	}

	duration := e.extractDurationFromContext(stepPlan.Context)
	if duration <= 0 {
		duration = 1000 // 默认等待1秒
	}

	step := AutomationStep{
		Type: "wait",
		Parameters: map[string]interface{}{
			"duration": float64(duration),
		},
	}

	opResult := e.automationService.ExecuteStep(step)
	if !opResult.Success {
		result.Error = fmt.Sprintf("等待操作失败: %s", opResult.Error)
		return result
	}

	result.Success = true
	result.Message = fmt.Sprintf("等待 %d 毫秒", duration)
	result.Data = map[string]interface{}{
		"duration": duration,
	}
	return result
}

// executeKeyPressStep 执行按键步骤
func (e *UnifiedExecutionEngine) executeKeyPressStep(stepPlan *domain.AutomationStepPlan) *domain.StepExecutionResult {
	result := &domain.StepExecutionResult{
		StepType: stepPlan.Type,
		Success:  false,
	}

	key, modifiers := e.extractKeyFromContext(stepPlan.Context)
	if key == "" {
		result.Error = "无法从上下文中提取按键信息"
		return result
	}

	step := AutomationStep{
		Type: "key",
		Parameters: map[string]interface{}{
			"key":       key,
			"modifiers": modifiers,
		},
	}

	opResult := e.automationService.ExecuteStep(step)
	if !opResult.Success {
		result.Error = fmt.Sprintf("按键操作失败: %s", opResult.Error)
		return result
	}

	result.Success = true
	result.Message = fmt.Sprintf("成功按键: %s", key)
	result.Data = map[string]interface{}{
		"key":       key,
		"modifiers": modifiers,
	}
	return result
}
