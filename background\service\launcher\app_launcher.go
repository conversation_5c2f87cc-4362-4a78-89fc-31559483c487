package launcher

import (
	"fmt"
	"log/slog"
	"os/exec"
	"strings"

	"diandian/background/service/appsearch"
)

// AppLauncher 跨平台应用启动器接口
type AppLauncher interface {
	// LaunchApp 启动应用程序
	LaunchApp(appIdentifier string) error

	// LaunchSystemApp 启动系统内置应用
	LaunchSystemApp(appName string) error

	// LaunchThirdPartyApp 启动第三方应用
	LaunchThirdPartyApp(appName string) error

	// LaunchDefaultBrowser 启动默认浏览器
	LaunchDefaultBrowser() error

	// FindAppPath 查找应用程序路径
	FindAppPath(appName string) (string, error)

	// LaunchWithFallback 使用退化重试启动应用
	LaunchWithFallback(appIdentifier string) error
}

// PlatformLauncher 平台特定启动器的基础结构
type PlatformLauncher struct {
	// 可以添加通用字段
	fallbackLauncher *UniversalFallbackLauncher
}

// initializeFallback 初始化退化重试启动器
func (pl *PlatformLauncher) initializeFallback() {
	if pl.fallbackLauncher == nil {
		pl.fallbackLauncher = NewUniversalFallbackLauncher()
	}
}

// LaunchWithFallback 使用退化重试启动应用（通用实现）
func (pl *PlatformLauncher) LaunchWithFallback(appIdentifier string) error {
	pl.initializeFallback()
	return pl.fallbackLauncher.LaunchWithFallback(appIdentifier)
}

// NewAppLauncher 创建平台特定的应用启动器
func NewAppLauncher() AppLauncher {
	return newPlatformLauncher()
}

// 注意：LaunchApp方法将由具体的平台实现提供，这里不需要通用实现

// launchByPath 通过路径启动应用（通用方法）
func (pl *PlatformLauncher) launchByPath(path string) error {
	slog.Info("通过路径启动应用", "path", path)

	cmd := exec.Command(path)
	err := cmd.Start()
	if err != nil {
		return fmt.Errorf("启动应用失败: %v", err)
	}

	slog.Info("应用启动成功", "path", path, "pid", cmd.Process.Pid)
	return nil
}

// findAppWithSearch 使用跨平台搜索查找应用（通用方法）
func (pl *PlatformLauncher) findAppWithSearch(appName string) (string, error) {
	slog.Info("使用跨平台搜索查找应用", "app", appName)

	// 使用新的跨平台搜索系统
	app, err := appsearch.FindApp(appName)
	if err != nil {
		slog.Debug("跨平台搜索失败", "app", appName, "error", err)
		return "", fmt.Errorf("未找到应用: %s", appName)
	}

	// 优先返回可执行文件路径
	if app.ExecutablePath != "" {
		slog.Info("通过跨平台搜索找到应用", "app", appName, "path", app.ExecutablePath, "confidence", app.Confidence)
		return app.ExecutablePath, nil
	}

	// 如果没有可执行文件路径，返回安装路径
	if app.InstallPath != "" {
		slog.Info("通过跨平台搜索找到应用安装路径", "app", appName, "path", app.InstallPath, "confidence", app.Confidence)
		return app.InstallPath, nil
	}

	return "", fmt.Errorf("找到应用但无有效路径: %s", appName)
}

// 通用的应用名称映射
func (pl *PlatformLauncher) getCommonAppMappings() map[string]string {
	return map[string]string{
		"记事本":        "notepad",
		"notepad":    "notepad",
		"计算器":        "calc",
		"calculator": "calc",
		"画图":         "mspaint",
		"paint":      "mspaint",
		"浏览器":        "browser",
		"browser":    "browser",
	}
}

// normalizeAppName 标准化应用名称
func (pl *PlatformLauncher) normalizeAppName(appName string) string {
	appName = strings.ToLower(strings.TrimSpace(appName))

	// 检查通用映射
	if mappings := pl.getCommonAppMappings(); mappings[appName] != "" {
		return mappings[appName]
	}

	return appName
}
