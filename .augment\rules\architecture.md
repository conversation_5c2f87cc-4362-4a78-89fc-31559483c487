---
type: "agent_requested"
description: "描述了系统目标和整体架构"
---

# 系统建设目标
本系统名为DianDian（点点小助理），主要目标是构建一个跨端的桌面应用，能够接入语言大模型和视觉大模型，提供AI聊天功能，并支持自动操作电脑进行自动化任务。

本系统旨在作为用户的AI助手，结合聊天界面和后台服务，实现智能交互和电脑自动化操作（computer use）

# 当前架构概述
## 技术栈
项目使用了最新的wails v3版本构建，后端采用Go语言，前端使用Vue3 + TypeScript

## 后端架构
- 后端主要代码位于background中：
  - app：应用窗口管理
  - service：核心服务，主要为绑定前端操作使用，也包含了数据的初始化服务
  - model：数据模型
  - database：公共的数据库操作
  - util：通用工具
  - constant：常量定义

## 前端架构
- 前端主要代码位于frontend中：
  - 采用Vite构建，并使用了element-plus、element-plus-x来支撑前端UI展示
  - 使用了Tailwind css
  - 绑定后端服务由wails3自动编译生成代码，位于bindings文件夹中，**不要修改其中的任何代码**

## 特别说明
- 对于前端用不到的Service，严禁在wails的Services中注册
- 严禁修改前端frontend/bindings下的代码，如果wails中注册的service发生改变，请执行`wails3 generate bindings`来重新生成