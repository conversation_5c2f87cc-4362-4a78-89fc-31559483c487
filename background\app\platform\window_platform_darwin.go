//go:build darwin

package platform

import (
	"os/exec"
	"strconv"
	"strings"

	"github.com/wailsapp/wails/v3/pkg/application"
)

// DarwinPlatform macOS平台的窗口管理器
type DarwinPlatform struct{}

// newPlatformWindow 创建macOS平台窗口管理器
func newPlatformWindow() WindowPlatform {
	return &DarwinPlatform{}
}

// GetScreenSize 获取macOS屏幕尺寸
func (dp *DarwinPlatform) GetScreenSize() ScreenInfo {
	// 使用system_profiler获取显示器信息
	cmd := exec.Command("system_profiler", "SPDisplaysDataType")
	output, err := cmd.Output()
	if err != nil {
		// 如果获取失败，返回默认值
		return ScreenInfo{Width: 1920, Height: 1080}
	}

	// 简化的解析逻辑，实际项目中可能需要更复杂的解析
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, "Resolution:") {
			// 解析分辨率信息
			if width, height := dp.parseResolution(line); width > 0 && height > 0 {
				return ScreenInfo{Width: width, Height: height}
			}
		}
	}

	// 备用方法：使用osascript
	return dp.getScreenSizeWithOsascript()
}

// parseResolution 解析分辨率字符串
func (dp *DarwinPlatform) parseResolution(line string) (int, int) {
	// 简化的解析逻辑，寻找类似 "1920 x 1080" 的模式
	parts := strings.Fields(line)
	for i, part := range parts {
		if part == "x" && i > 0 && i < len(parts)-1 {
			if width, err := strconv.Atoi(parts[i-1]); err == nil {
				if height, err := strconv.Atoi(parts[i+1]); err == nil {
					return width, height
				}
			}
		}
	}
	return 0, 0
}

// getScreenSizeWithOsascript 使用osascript获取屏幕尺寸
func (dp *DarwinPlatform) getScreenSizeWithOsascript() ScreenInfo {
	// 获取屏幕宽度
	widthCmd := exec.Command("osascript", "-e", "tell application \"Finder\" to get bounds of window of desktop")
	widthOutput, err := widthCmd.Output()
	if err != nil {
		return ScreenInfo{Width: 1920, Height: 1080}
	}

	// 解析输出，格式通常是 "0, 0, width, height"
	bounds := strings.TrimSpace(string(widthOutput))
	parts := strings.Split(bounds, ", ")
	if len(parts) >= 4 {
		if width, err := strconv.Atoi(parts[2]); err == nil {
			if height, err := strconv.Atoi(parts[3]); err == nil {
				return ScreenInfo{Width: width, Height: height}
			}
		}
	}

	return ScreenInfo{Width: 1920, Height: 1080}
}

// GetPlatformWindowOptions 获取macOS平台特定的窗口选项
func (dp *DarwinPlatform) GetPlatformWindowOptions() interface{} {
	return application.MacWindow{
		InvisibleTitleBarHeight: 50,
		Backdrop:                application.MacBackdropTranslucent,
		TitleBar:                application.MacTitleBarHiddenInset,
	}
}

// GetFloatingWindowOptions 获取macOS浮动窗口的平台特定选项
func (dp *DarwinPlatform) GetFloatingWindowOptions() interface{} {
	return application.MacWindow{
		InvisibleTitleBarHeight: 0,
		Backdrop:                application.MacBackdropTransparent,
		TitleBar:                application.MacTitleBarHidden,
	}
}
