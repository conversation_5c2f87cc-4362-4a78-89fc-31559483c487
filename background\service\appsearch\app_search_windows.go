//go:build windows

package appsearch

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"golang.org/x/sys/windows/registry"
)

// WindowsAppSearcher Windows平台的应用搜索器
type WindowsAppSearcher struct {
	cache      map[string]*AppInfo
	cacheTime  time.Time
	cacheMutex sync.RWMutex
}

// newPlatformSearcher 创建Windows平台搜索器
func newPlatformSearcher() AppSearcher {
	return &WindowsAppSearcher{
		cache: make(map[string]*AppInfo),
	}
}

// SearchApps 搜索应用程序
func (w *WindowsAppSearcher) SearchApps(ctx context.Context, query string, options *SearchOptions) *SearchResult {
	start := time.Now()
	result := &SearchResult{
		Apps:     make([]*AppInfo, 0),
		Strategy: "windows_multi_strategy",
	}

	// 设置默认搜索路径
	if len(options.SearchPaths) == 0 {
		options.SearchPaths = []string{
			`C:\Program Files`,
			`C:\Program Files (x86)`,
			filepath.Join(os.Getenv("LOCALAPPDATA"), "Programs"),
			filepath.Join(os.Getenv("APPDATA"), "Microsoft", "Windows", "Start Menu", "Programs"),
		}
	}

	var allApps []*AppInfo

	// 策略1: WMI查询已安装程序
	if options.UseRegistry {
		if wmiApps := w.searchByWMI(ctx, query); len(wmiApps) > 0 {
			allApps = append(allApps, wmiApps...)
			slog.Debug("WMI搜索找到应用", "count", len(wmiApps))
		}
	}

	// 策略2: 增强注册表搜索
	if options.UseRegistry {
		if regApps := w.searchByRegistry(ctx, query); len(regApps) > 0 {
			allApps = append(allApps, regApps...)
			slog.Debug("注册表搜索找到应用", "count", len(regApps))
		}
	}

	// 策略3: 文件系统搜索
	if options.UseFileSystem {
		if fsApps := w.searchByFileSystem(ctx, query, options.SearchPaths, options.MaxDepth); len(fsApps) > 0 {
			allApps = append(allApps, fsApps...)
			slog.Debug("文件系统搜索找到应用", "count", len(fsApps))
		}
	}

	// 策略4: Windows Search API
	if options.UseSystemSearch {
		if searchApps := w.searchByWindowsSearch(ctx, query); len(searchApps) > 0 {
			allApps = append(allApps, searchApps...)
			slog.Debug("Windows搜索找到应用", "count", len(searchApps))
		}
	}

	// 去重和排序
	uniqueApps := w.deduplicateApps(allApps)

	// 计算置信度并过滤
	for _, app := range uniqueApps {
		confidence := CalculateConfidence(query, app)
		if confidence >= options.MinConfidence {
			app.Confidence = confidence
			result.Apps = append(result.Apps, app)
		}
	}

	// 按置信度排序
	w.sortAppsByConfidence(result.Apps)

	// 限制结果数量
	if options.MaxResults > 0 && len(result.Apps) > options.MaxResults {
		result.Apps = result.Apps[:options.MaxResults]
	}

	result.Duration = time.Since(start)
	return result
}

// FindApp 查找单个应用程序
func (w *WindowsAppSearcher) FindApp(ctx context.Context, appName string, options *SearchOptions) (*AppInfo, error) {
	result := w.SearchApps(ctx, appName, options)
	if result.Error != nil {
		return nil, result.Error
	}

	if len(result.Apps) == 0 {
		return nil, fmt.Errorf("未找到应用: %s", appName)
	}

	return result.Apps[0], nil
}

// GetInstalledApps 获取所有已安装的应用程序
func (w *WindowsAppSearcher) GetInstalledApps(ctx context.Context, options *SearchOptions) *SearchResult {
	return w.SearchApps(ctx, "", options)
}

// RefreshCache 刷新应用缓存
func (w *WindowsAppSearcher) RefreshCache(ctx context.Context) error {
	w.cacheMutex.Lock()
	defer w.cacheMutex.Unlock()

	w.cache = make(map[string]*AppInfo)
	w.cacheTime = time.Now()
	return nil
}

// GetCacheStats 获取缓存统计信息
func (w *WindowsAppSearcher) GetCacheStats() map[string]interface{} {
	w.cacheMutex.RLock()
	defer w.cacheMutex.RUnlock()

	return map[string]interface{}{
		"cache_size": len(w.cache),
		"cache_time": w.cacheTime,
		"platform":   "windows",
	}
}

// searchByWMI 使用WMI查询已安装程序
func (w *WindowsAppSearcher) searchByWMI(ctx context.Context, query string) []*AppInfo {
	var apps []*AppInfo

	// 使用PowerShell调用WMI
	cmd := exec.CommandContext(ctx, "powershell", "-Command",
		`Get-WmiObject -Class Win32_Product | Select-Object Name, Version, Vendor, InstallLocation | ConvertTo-Json`)

	output, err := cmd.Output()
	if err != nil {
		slog.Debug("WMI查询失败", "error", err)
		return apps
	}

	// 解析JSON输出并转换为AppInfo
	// 这里简化处理，实际项目中需要完整的JSON解析
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(strings.ToLower(line), strings.ToLower(query)) {
			// 简化的解析逻辑
			if app := w.parseWMILine(line); app != nil {
				apps = append(apps, app)
			}
		}
	}

	return apps
}

// searchByRegistry 增强的注册表搜索
func (w *WindowsAppSearcher) searchByRegistry(ctx context.Context, query string) []*AppInfo {
	var apps []*AppInfo

	// 搜索多个注册表位置
	registryPaths := []struct {
		root registry.Key
		path string
	}{
		{registry.LOCAL_MACHINE, `SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall`},
		{registry.LOCAL_MACHINE, `SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall`},
		{registry.CURRENT_USER, `SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall`},
	}

	for _, regPath := range registryPaths {
		if regApps := w.searchRegistryPath(regPath.root, regPath.path, query); len(regApps) > 0 {
			apps = append(apps, regApps...)
		}
	}

	return apps
}

// searchRegistryPath 搜索特定注册表路径
func (w *WindowsAppSearcher) searchRegistryPath(root registry.Key, path, query string) []*AppInfo {
	var apps []*AppInfo

	key, err := registry.OpenKey(root, path, registry.ENUMERATE_SUB_KEYS)
	if err != nil {
		return apps
	}
	defer key.Close()

	subkeys, err := key.ReadSubKeyNames(-1)
	if err != nil {
		return apps
	}

	for _, subkey := range subkeys {
		if app := w.parseRegistryEntry(root, path+"\\"+subkey, query); app != nil {
			apps = append(apps, app)
		}
	}

	return apps
}

// parseRegistryEntry 解析注册表条目
func (w *WindowsAppSearcher) parseRegistryEntry(root registry.Key, path, query string) *AppInfo {
	key, err := registry.OpenKey(root, path, registry.QUERY_VALUE)
	if err != nil {
		return nil
	}
	defer key.Close()

	// 读取应用信息
	displayName, _, _ := key.GetStringValue("DisplayName")
	if displayName == "" {
		return nil
	}

	// 检查是否匹配查询
	if query != "" && !containsIgnoreCase(displayName, query) {
		return nil
	}

	app := &AppInfo{
		DisplayName: displayName,
		Name:        displayName,
	}

	// 读取其他信息
	if version, _, err := key.GetStringValue("DisplayVersion"); err == nil {
		app.Version = version
	}

	if publisher, _, err := key.GetStringValue("Publisher"); err == nil {
		app.Publisher = publisher
	}

	if installLocation, _, err := key.GetStringValue("InstallLocation"); err == nil {
		app.InstallPath = installLocation
		// 尝试找到可执行文件
		if execPath := w.findExecutableInPath(installLocation, displayName); execPath != "" {
			app.ExecutablePath = execPath
		}
	}

	if iconPath, _, err := key.GetStringValue("DisplayIcon"); err == nil {
		app.IconPath = iconPath
	}

	return app
}

// searchByFileSystem 文件系统搜索
func (w *WindowsAppSearcher) searchByFileSystem(ctx context.Context, query string, searchPaths []string, maxDepth int) []*AppInfo {
	var apps []*AppInfo

	for _, searchPath := range searchPaths {
		if fsApps := w.searchInDirectory(searchPath, query, maxDepth); len(fsApps) > 0 {
			apps = append(apps, fsApps...)
		}
	}

	return apps
}

// searchInDirectory 在目录中搜索应用
func (w *WindowsAppSearcher) searchInDirectory(dirPath, query string, maxDepth int) []*AppInfo {
	var apps []*AppInfo

	if maxDepth <= 0 {
		return apps
	}

	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return apps
	}

	for _, entry := range entries {
		fullPath := filepath.Join(dirPath, entry.Name())

		if entry.IsDir() {
			// 检查目录名是否匹配
			if query == "" || containsIgnoreCase(entry.Name(), query) {
				if execPath := w.findExecutableInPath(fullPath, entry.Name()); execPath != "" {
					app := &AppInfo{
						Name:           entry.Name(),
						DisplayName:    entry.Name(),
						ExecutablePath: execPath,
						InstallPath:    fullPath,
					}
					apps = append(apps, app)
				}
			}

			// 递归搜索子目录
			if subApps := w.searchInDirectory(fullPath, query, maxDepth-1); len(subApps) > 0 {
				apps = append(apps, subApps...)
			}
		} else if strings.HasSuffix(strings.ToLower(entry.Name()), ".exe") {
			// 检查可执行文件名是否匹配
			if query == "" || containsIgnoreCase(entry.Name(), query) {
				appName := strings.TrimSuffix(entry.Name(), ".exe")
				app := &AppInfo{
					Name:           appName,
					DisplayName:    appName,
					ExecutablePath: fullPath,
					InstallPath:    dirPath,
				}
				apps = append(apps, app)
			}
		}
	}

	return apps
}

// searchByWindowsSearch 使用Windows Search API
func (w *WindowsAppSearcher) searchByWindowsSearch(ctx context.Context, query string) []*AppInfo {
	var apps []*AppInfo

	// 使用PowerShell调用Windows Search
	searchQuery := fmt.Sprintf(`Get-ChildItem -Path "C:\ProgramData\Microsoft\Windows\Start Menu\Programs", "$env:APPDATA\Microsoft\Windows\Start Menu\Programs" -Recurse -Filter "*.lnk" | Where-Object { $_.Name -like "*%s*" }`, query)

	cmd := exec.CommandContext(ctx, "powershell", "-Command", searchQuery)
	output, err := cmd.Output()
	if err != nil {
		slog.Debug("Windows搜索失败", "error", err)
		return apps
	}

	// 解析快捷方式
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" && strings.HasSuffix(line, ".lnk") {
			if app := w.parseShortcut(line); app != nil {
				apps = append(apps, app)
			}
		}
	}

	return apps
}

// findExecutableInPath 在路径中查找可执行文件
func (w *WindowsAppSearcher) findExecutableInPath(dirPath, appName string) string {
	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		return ""
	}

	// 常见的可执行文件模式
	patterns := []string{
		appName + ".exe",
		strings.ToLower(appName) + ".exe",
		strings.ReplaceAll(appName, " ", "") + ".exe",
		strings.ReplaceAll(strings.ToLower(appName), " ", "") + ".exe",
	}

	for _, pattern := range patterns {
		execPath := filepath.Join(dirPath, pattern)
		if _, err := os.Stat(execPath); err == nil {
			return execPath
		}
	}

	// 搜索子目录中的可执行文件
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return ""
	}

	for _, entry := range entries {
		if strings.HasSuffix(strings.ToLower(entry.Name()), ".exe") {
			if containsIgnoreCase(entry.Name(), appName) {
				return filepath.Join(dirPath, entry.Name())
			}
		}
	}

	return ""
}

// 辅助方法
func (w *WindowsAppSearcher) parseWMILine(line string) *AppInfo {
	// 简化的WMI输出解析
	// 实际项目中需要完整的JSON解析
	return nil
}

func (w *WindowsAppSearcher) parseShortcut(shortcutPath string) *AppInfo {
	// 解析快捷方式文件
	// 这里简化处理，实际需要解析.lnk文件格式
	name := strings.TrimSuffix(filepath.Base(shortcutPath), ".lnk")
	return &AppInfo{
		Name:        name,
		DisplayName: name,
		IconPath:    shortcutPath,
	}
}

func (w *WindowsAppSearcher) deduplicateApps(apps []*AppInfo) []*AppInfo {
	seen := make(map[string]bool)
	var unique []*AppInfo

	for _, app := range apps {
		key := strings.ToLower(app.Name + "|" + app.ExecutablePath)
		if !seen[key] {
			seen[key] = true
			unique = append(unique, app)
		}
	}

	return unique
}

func (w *WindowsAppSearcher) sortAppsByConfidence(apps []*AppInfo) {
	// 简单的冒泡排序，按置信度降序
	for i := 0; i < len(apps)-1; i++ {
		for j := 0; j < len(apps)-i-1; j++ {
			if apps[j].Confidence < apps[j+1].Confidence {
				apps[j], apps[j+1] = apps[j+1], apps[j]
			}
		}
	}
}
