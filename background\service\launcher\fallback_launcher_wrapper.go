package launcher

import (
	"fmt"
	"log/slog"
	"time"
)

// FallbackLauncher 退化重试启动器（兼容性包装器）
type FallbackLauncher struct {
	strategies []UniversalLaunchStrategy
}

// UniversalLaunchStrategy 通用启动策略接口（兼容性接口）
type UniversalLaunchStrategy interface {
	Name() string
	CanHandle(appIdentifier string) bool
	Launch(appIdentifier string) error
	Priority() int
}

// NewFallbackLauncher 创建退化重试启动器
func NewFallbackLauncher() *FallbackLauncher {
	// 使用新的通用退化启动器
	universalLauncher := NewUniversalFallbackLauncher()
	
	fl := &FallbackLauncher{}
	
	// 将通用启动器包装为旧接口
	fl.strategies = []UniversalLaunchStrategy{
		&UniversalLauncherWrapper{launcher: universalLauncher},
	}
	
	return fl
}

// UniversalLauncherWrapper 包装器，将新的通用启动器适配到旧接口
type UniversalLauncherWrapper struct {
	launcher *UniversalFallbackLauncher
}

func (w *UniversalLauncherWrapper) Name() string                       { return "UniversalFallback" }
func (w *UniversalLauncherWrapper) Priority() int                      { return 1 }
func (w *UniversalLauncherWrapper) CanHandle(appIdentifier string) bool { return true }

func (w *UniversalLauncherWrapper) Launch(appIdentifier string) error {
	return w.launcher.LaunchWithFallback(appIdentifier)
}

// LaunchWithFallback 使用退化重试启动应用
func (fl *FallbackLauncher) LaunchWithFallback(appIdentifier string) error {
	slog.Info("开始退化重试启动", "app", appIdentifier, "strategies", len(fl.strategies))
	
	var lastError error
	
	for i, strategy := range fl.strategies {
		if !strategy.CanHandle(appIdentifier) {
			slog.Debug("策略不支持此应用", "strategy", strategy.Name(), "app", appIdentifier)
			continue
		}
		
		slog.Info("尝试启动策略", "strategy", strategy.Name(), "priority", strategy.Priority(), "attempt", i+1)
		
		err := strategy.Launch(appIdentifier)
		if err == nil {
			slog.Info("启动成功", "strategy", strategy.Name(), "app", appIdentifier)
			return nil
		}
		
		slog.Warn("策略失败，尝试下一个", "strategy", strategy.Name(), "error", err)
		lastError = err
		
		// 策略间等待，避免冲突
		time.Sleep(500 * time.Millisecond)
	}
	
	return fmt.Errorf("所有策略都失败了，最后错误: %v", lastError)
}
