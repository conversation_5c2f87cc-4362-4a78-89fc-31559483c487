//go:build darwin

package appsearch

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// DarwinAppSearcher macOS平台的应用搜索器
type DarwinAppSearcher struct {
	cache      map[string]*AppInfo
	cacheTime  time.Time
	cacheMutex sync.RWMutex
}

// newPlatformSearcher 创建macOS平台搜索器
func newPlatformSearcher() AppSearcher {
	return &DarwinAppSearcher{
		cache: make(map[string]*AppInfo),
	}
}

// SearchApps 搜索应用程序
func (d *DarwinAppSearcher) SearchApps(ctx context.Context, query string, options *SearchOptions) *SearchResult {
	start := time.Now()
	result := &SearchResult{
		Apps:     make([]*AppInfo, 0),
		Strategy: "darwin_multi_strategy",
	}

	// 设置默认搜索路径
	if len(options.SearchPaths) == 0 {
		options.SearchPaths = []string{
			"/Applications",
			"/System/Applications",
			filepath.Join(os.Getenv("HOME"), "Applications"),
			"/usr/local/bin",
			"/opt/homebrew/bin",
		}
	}

	var allApps []*AppInfo

	// 策略1: Spotlight搜索
	if options.UseSystemSearch {
		if spotlightApps := d.searchBySpotlight(ctx, query); len(spotlightApps) > 0 {
			allApps = append(allApps, spotlightApps...)
			slog.Debug("Spotlight搜索找到应用", "count", len(spotlightApps))
		}
	}

	// 策略2: Applications文件夹扫描
	if options.UseFileSystem {
		if fsApps := d.searchByFileSystem(ctx, query, options.SearchPaths); len(fsApps) > 0 {
			allApps = append(allApps, fsApps...)
			slog.Debug("文件系统搜索找到应用", "count", len(fsApps))
		}
	}

	// 策略3: LaunchServices查询
	if options.UseRegistry {
		if lsApps := d.searchByLaunchServices(ctx, query); len(lsApps) > 0 {
			allApps = append(allApps, lsApps...)
			slog.Debug("LaunchServices搜索找到应用", "count", len(lsApps))
		}
	}

	// 去重和排序
	uniqueApps := d.deduplicateApps(allApps)
	
	// 计算置信度并过滤
	for _, app := range uniqueApps {
		confidence := CalculateConfidence(query, app)
		if confidence >= options.MinConfidence {
			app.Confidence = confidence
			result.Apps = append(result.Apps, app)
		}
	}

	// 按置信度排序
	d.sortAppsByConfidence(result.Apps)

	// 限制结果数量
	if options.MaxResults > 0 && len(result.Apps) > options.MaxResults {
		result.Apps = result.Apps[:options.MaxResults]
	}

	result.Duration = time.Since(start)
	return result
}

// FindApp 查找单个应用程序
func (d *DarwinAppSearcher) FindApp(ctx context.Context, appName string, options *SearchOptions) (*AppInfo, error) {
	result := d.SearchApps(ctx, appName, options)
	if result.Error != nil {
		return nil, result.Error
	}
	
	if len(result.Apps) == 0 {
		return nil, fmt.Errorf("未找到应用: %s", appName)
	}
	
	return result.Apps[0], nil
}

// GetInstalledApps 获取所有已安装的应用程序
func (d *DarwinAppSearcher) GetInstalledApps(ctx context.Context, options *SearchOptions) *SearchResult {
	return d.SearchApps(ctx, "", options)
}

// RefreshCache 刷新应用缓存
func (d *DarwinAppSearcher) RefreshCache(ctx context.Context) error {
	d.cacheMutex.Lock()
	defer d.cacheMutex.Unlock()
	
	d.cache = make(map[string]*AppInfo)
	d.cacheTime = time.Now()
	return nil
}

// GetCacheStats 获取缓存统计信息
func (d *DarwinAppSearcher) GetCacheStats() map[string]interface{} {
	d.cacheMutex.RLock()
	defer d.cacheMutex.RUnlock()
	
	return map[string]interface{}{
		"cache_size":  len(d.cache),
		"cache_time":  d.cacheTime,
		"platform":    "darwin",
	}
}

// searchBySpotlight 使用Spotlight搜索
func (d *DarwinAppSearcher) searchBySpotlight(ctx context.Context, query string) []*AppInfo {
	var apps []*AppInfo
	
	// 使用mdfind命令调用Spotlight
	cmd := exec.CommandContext(ctx, "mdfind", 
		"-onlyin", "/Applications",
		"-onlyin", "/System/Applications", 
		fmt.Sprintf("kMDItemKind == 'Application' && kMDItemDisplayName == '*%s*'", query))
	
	output, err := cmd.Output()
	if err != nil {
		slog.Debug("Spotlight搜索失败", "error", err)
		return apps
	}
	
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" && strings.HasSuffix(line, ".app") {
			if app := d.parseAppBundle(line); app != nil {
				apps = append(apps, app)
			}
		}
	}
	
	return apps
}

// searchByFileSystem 文件系统搜索
func (d *DarwinAppSearcher) searchByFileSystem(ctx context.Context, query string, searchPaths []string) []*AppInfo {
	var apps []*AppInfo
	
	for _, searchPath := range searchPaths {
		if fsApps := d.searchInDirectory(searchPath, query); len(fsApps) > 0 {
			apps = append(apps, fsApps...)
		}
	}
	
	return apps
}

// searchInDirectory 在目录中搜索应用
func (d *DarwinAppSearcher) searchInDirectory(dirPath, query string) []*AppInfo {
	var apps []*AppInfo
	
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return apps
	}
	
	for _, entry := range entries {
		fullPath := filepath.Join(dirPath, entry.Name())
		
		if entry.IsDir() && strings.HasSuffix(entry.Name(), ".app") {
			// macOS应用程序包
			if query == "" || containsIgnoreCase(entry.Name(), query) {
				if app := d.parseAppBundle(fullPath); app != nil {
					apps = append(apps, app)
				}
			}
		} else if !entry.IsDir() {
			// 可执行文件
			if query == "" || containsIgnoreCase(entry.Name(), query) {
				app := &AppInfo{
					Name:           entry.Name(),
					DisplayName:    entry.Name(),
					ExecutablePath: fullPath,
					InstallPath:    dirPath,
				}
				apps = append(apps, app)
			}
		}
	}
	
	return apps
}

// searchByLaunchServices 使用LaunchServices查询
func (d *DarwinAppSearcher) searchByLaunchServices(ctx context.Context, query string) []*AppInfo {
	var apps []*AppInfo
	
	// 使用system_profiler获取应用信息
	cmd := exec.CommandContext(ctx, "system_profiler", "SPApplicationsDataType", "-xml")
	output, err := cmd.Output()
	if err != nil {
		slog.Debug("LaunchServices查询失败", "error", err)
		return apps
	}
	
	// 简化处理：解析XML输出
	// 实际项目中需要完整的XML解析
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(strings.ToLower(line), strings.ToLower(query)) {
			// 简化的解析逻辑
			if app := d.parseLaunchServicesLine(line); app != nil {
				apps = append(apps, app)
			}
		}
	}
	
	return apps
}

// parseAppBundle 解析macOS应用程序包
func (d *DarwinAppSearcher) parseAppBundle(appPath string) *AppInfo {
	if !strings.HasSuffix(appPath, ".app") {
		return nil
	}
	
	appName := strings.TrimSuffix(filepath.Base(appPath), ".app")
	app := &AppInfo{
		Name:           appName,
		DisplayName:    appName,
		InstallPath:    appPath,
		ExecutablePath: appPath, // macOS中.app本身就是可执行的
	}
	
	// 尝试读取Info.plist获取更多信息
	infoPlistPath := filepath.Join(appPath, "Contents", "Info.plist")
	if _, err := os.Stat(infoPlistPath); err == nil {
		// 这里可以解析plist文件获取版本、发布者等信息
		// 简化处理，实际需要plist解析库
	}
	
	return app
}

// 辅助方法
func (d *DarwinAppSearcher) parseLaunchServicesLine(line string) *AppInfo {
	// 简化的LaunchServices输出解析
	// 实际项目中需要完整的XML解析
	return nil
}

func (d *DarwinAppSearcher) deduplicateApps(apps []*AppInfo) []*AppInfo {
	seen := make(map[string]bool)
	var unique []*AppInfo
	
	for _, app := range apps {
		key := strings.ToLower(app.Name + "|" + app.ExecutablePath)
		if !seen[key] {
			seen[key] = true
			unique = append(unique, app)
		}
	}
	
	return unique
}

func (d *DarwinAppSearcher) sortAppsByConfidence(apps []*AppInfo) {
	// 简单的冒泡排序，按置信度降序
	for i := 0; i < len(apps)-1; i++ {
		for j := 0; j < len(apps)-i-1; j++ {
			if apps[j].Confidence < apps[j+1].Confidence {
				apps[j], apps[j+1] = apps[j+1], apps[j]
			}
		}
	}
}
