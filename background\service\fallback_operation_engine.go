package service

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"strings"

	"diandian/background/domain"
)

// FallbackOperation 保底操作定义
type FallbackOperation struct {
	Type        string                 `json:"type"`        // click, type, key, scroll
	Description string                 `json:"description"` // 操作描述
	Parameters  map[string]interface{} `json:"parameters"`  // 操作参数
	Confidence  float64                `json:"confidence"`  // 置信度
	Reasoning   string                 `json:"reasoning"`   // 推理过程
}

// generateFallbackOperation 生成保底操作
func (e *UnifiedExecutionEngine) generateFallbackOperation(stepPlan *domain.AutomationStepPlan, analysis *domain.VisualAnalysisResponse) (*FallbackOperation, error) {
	slog.Info("开始生成保底操作", "step_type", stepPlan.Type, "elements_found", len(analysis.ElementsFound))

	// 构建LLM提示
	prompt := e.buildFallbackPrompt(stepPlan, analysis)

	// 调用LLM生成操作
	response, err := e.llmService.GenerateFallbackOperation(prompt)
	if err != nil {
		return nil, fmt.Errorf("LLM生成保底操作失败: %v", err)
	}

	// 解析LLM响应
	operation, err := e.parseFallbackResponse(response)
	if err != nil {
		return nil, fmt.Errorf("解析保底操作响应失败: %v", err)
	}

	// 验证操作的安全性
	if err := e.validateFallbackOperation(operation); err != nil {
		return nil, fmt.Errorf("保底操作安全验证失败: %v", err)
	}

	slog.Info("保底操作生成成功",
		"type", operation.Type,
		"confidence", operation.Confidence,
		"description", operation.Description)

	return operation, nil
}

// buildFallbackPrompt 构建保底操作的LLM提示
func (e *UnifiedExecutionEngine) buildFallbackPrompt(stepPlan *domain.AutomationStepPlan, analysis *domain.VisualAnalysisResponse) string {
	prompt := fmt.Sprintf(`
作为一个智能自动化助手，我需要根据当前屏幕状态生成具体的鼠标键盘操作来完成任务。

原始任务步骤:
- 类型: %s
- 描述: %s
- 上下文: %s

当前屏幕分析结果:
- 屏幕描述: %s
- 找到的元素数量: %d

`, stepPlan.Type, stepPlan.Description, stepPlan.Context, analysis.ScreenDescription, len(analysis.ElementsFound))

	// 添加找到的元素信息
	if len(analysis.ElementsFound) > 0 {
		prompt += "找到的可交互元素:\n"
		for i, element := range analysis.ElementsFound {
			prompt += fmt.Sprintf("%d. %s - %s (坐标: %d,%d, 大小: %dx%d, 置信度: %.2f)\n",
				i+1, element.Type, element.Description,
				element.Coordinates.X, element.Coordinates.Y,
				element.Coordinates.Width, element.Coordinates.Height,
				element.Confidence)
		}
	}

	// 添加操作建议
	if len(analysis.Recommendations) > 0 {
		prompt += "\n系统建议的操作:\n"
		for i, rec := range analysis.Recommendations {
			prompt += fmt.Sprintf("%d. %s: %s (%s)\n", i+1, rec.Action, rec.Target, rec.Reason)
		}
	}

	prompt += `
请根据以上信息，生成一个具体的操作指令来完成原始任务。

要求:
1. 操作必须是具体的鼠标或键盘动作
2. 如果是点击操作，必须提供精确的坐标
3. 如果是输入操作，必须提供具体的文本内容
4. 操作必须安全，不能执行危险的系统命令
5. 提供操作的置信度评估(0-1)
6. 解释选择这个操作的原因

请以JSON格式返回，格式如下:
{
  "type": "click|type|key|scroll",
  "description": "操作描述",
  "parameters": {
    // 根据操作类型提供相应参数
    // click: {"x": 100, "y": 200, "button": "left"}
    // type: {"text": "要输入的文本"}
    // key: {"key": "Return", "modifiers": ["ctrl"]}
    // scroll: {"direction": "down", "amount": 3}
  },
  "confidence": 0.85,
  "reasoning": "选择这个操作的原因"
}
`

	return prompt
}

// parseFallbackResponse 解析LLM的保底操作响应
func (e *UnifiedExecutionEngine) parseFallbackResponse(response string) (*FallbackOperation, error) {
	// 清理响应文本，移除可能的markdown标记
	response = strings.TrimSpace(response)
	if strings.HasPrefix(response, "```json") {
		response = strings.TrimPrefix(response, "```json")
	}
	if strings.HasPrefix(response, "```") {
		response = strings.TrimPrefix(response, "```")
	}
	if strings.HasSuffix(response, "```") {
		response = strings.TrimSuffix(response, "```")
	}
	response = strings.TrimSpace(response)

	var operation FallbackOperation
	if err := json.Unmarshal([]byte(response), &operation); err != nil {
		return nil, fmt.Errorf("JSON解析失败: %v, 响应内容: %s", err, response)
	}

	// 验证必要字段
	if operation.Type == "" {
		return nil, fmt.Errorf("操作类型不能为空")
	}

	if operation.Parameters == nil {
		operation.Parameters = make(map[string]interface{})
	}

	// 设置默认置信度
	if operation.Confidence <= 0 {
		operation.Confidence = 0.5
	}

	return &operation, nil
}

// validateFallbackOperation 验证保底操作的安全性
func (e *UnifiedExecutionEngine) validateFallbackOperation(operation *FallbackOperation) error {
	// 验证操作类型
	validTypes := []string{"click", "type", "key", "scroll", "wait"}
	isValidType := false
	for _, validType := range validTypes {
		if operation.Type == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		return fmt.Errorf("不支持的操作类型: %s", operation.Type)
	}

	// 验证具体操作参数
	switch operation.Type {
	case "click":
		return e.validateClickOperation(operation)
	case "type":
		return e.validateTypeOperation(operation)
	case "key":
		return e.validateKeyOperation(operation)
	case "scroll":
		return e.validateScrollOperation(operation)
	}

	return nil
}

// validateClickOperation 验证点击操作
func (e *UnifiedExecutionEngine) validateClickOperation(operation *FallbackOperation) error {
	x, xOk := operation.Parameters["x"].(float64)
	y, yOk := operation.Parameters["y"].(float64)

	if !xOk || !yOk {
		return fmt.Errorf("点击操作缺少有效的坐标参数")
	}

	// 验证坐标范围（假设屏幕最大4K分辨率）
	if x < 0 || x > 3840 || y < 0 || y > 2160 {
		return fmt.Errorf("点击坐标超出合理范围: (%f, %f)", x, y)
	}

	// 验证按钮类型
	if button, ok := operation.Parameters["button"].(string); ok {
		validButtons := []string{"left", "right", "middle"}
		isValidButton := false
		for _, validButton := range validButtons {
			if button == validButton {
				isValidButton = true
				break
			}
		}
		if !isValidButton {
			return fmt.Errorf("无效的鼠标按钮: %s", button)
		}
	}

	return nil
}

// validateTypeOperation 验证输入操作
func (e *UnifiedExecutionEngine) validateTypeOperation(operation *FallbackOperation) error {
	text, ok := operation.Parameters["text"].(string)
	if !ok {
		return fmt.Errorf("输入操作缺少文本参数")
	}

	// 验证文本长度
	if len(text) > 1000 {
		return fmt.Errorf("输入文本过长: %d 字符", len(text))
	}

	// 检查危险字符或命令
	dangerousPatterns := []string{
		"rm -rf", "del /", "format", "shutdown", "reboot",
		"sudo", "admin", "password", "passwd",
	}

	textLower := strings.ToLower(text)
	for _, pattern := range dangerousPatterns {
		if strings.Contains(textLower, pattern) {
			return fmt.Errorf("输入文本包含潜在危险内容: %s", pattern)
		}
	}

	return nil
}

// validateKeyOperation 验证按键操作
func (e *UnifiedExecutionEngine) validateKeyOperation(operation *FallbackOperation) error {
	key, ok := operation.Parameters["key"].(string)
	if !ok {
		return fmt.Errorf("按键操作缺少按键参数")
	}

	// 验证按键名称
	validKeys := []string{
		"Return", "Enter", "Tab", "Space", "Escape", "Backspace", "Delete",
		"Up", "Down", "Left", "Right", "Home", "End", "PageUp", "PageDown",
		"F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12",
		"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m",
		"n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z",
		"0", "1", "2", "3", "4", "5", "6", "7", "8", "9",
	}

	isValidKey := false
	for _, validKey := range validKeys {
		if key == validKey {
			isValidKey = true
			break
		}
	}
	if !isValidKey {
		return fmt.Errorf("无效的按键: %s", key)
	}

	// 验证修饰键
	if modifiers, ok := operation.Parameters["modifiers"].([]interface{}); ok {
		validModifiers := []string{"ctrl", "alt", "shift", "cmd", "meta"}
		for _, mod := range modifiers {
			if modStr, ok := mod.(string); ok {
				isValidMod := false
				for _, validMod := range validModifiers {
					if modStr == validMod {
						isValidMod = true
						break
					}
				}
				if !isValidMod {
					return fmt.Errorf("无效的修饰键: %s", modStr)
				}
			}
		}
	}

	return nil
}

// validateScrollOperation 验证滚动操作
func (e *UnifiedExecutionEngine) validateScrollOperation(operation *FallbackOperation) error {
	direction, ok := operation.Parameters["direction"].(string)
	if !ok {
		return fmt.Errorf("滚动操作缺少方向参数")
	}

	validDirections := []string{"up", "down", "left", "right"}
	isValidDirection := false
	for _, validDir := range validDirections {
		if direction == validDir {
			isValidDirection = true
			break
		}
	}
	if !isValidDirection {
		return fmt.Errorf("无效的滚动方向: %s", direction)
	}

	// 验证滚动量
	if amount, ok := operation.Parameters["amount"].(float64); ok {
		if amount < 1 || amount > 10 {
			return fmt.Errorf("滚动量超出合理范围: %f", amount)
		}
	}

	return nil
}

// executeLLMGeneratedOperation 执行LLM生成的操作
func (e *UnifiedExecutionEngine) executeLLMGeneratedOperation(operation *FallbackOperation) *domain.StepExecutionResult {
	result := &domain.StepExecutionResult{
		StepType: "fallback_" + operation.Type,
		Success:  false,
	}

	slog.Info("执行LLM生成的保底操作",
		"type", operation.Type,
		"confidence", operation.Confidence,
		"description", operation.Description)

	// 构建AutomationStep
	step := AutomationStep{
		Type:       operation.Type,
		Parameters: operation.Parameters,
	}

	// 执行操作
	opResult := e.automationService.ExecuteStep(step)
	if opResult.Success {
		result.Success = true
		result.Message = fmt.Sprintf("保底操作执行成功: %s (置信度: %.2f)", operation.Description, operation.Confidence)
		result.Data = map[string]interface{}{
			"operation":   operation,
			"confidence":  operation.Confidence,
			"reasoning":   operation.Reasoning,
		}
	} else {
		result.Error = fmt.Sprintf("保底操作执行失败: %s", opResult.Error)
	}

	return result
}
