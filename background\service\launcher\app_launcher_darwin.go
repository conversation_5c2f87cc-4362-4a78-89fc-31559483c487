//go:build darwin

package launcher

import (
	"fmt"
	"log/slog"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

// DarwinLauncher macOS平台的应用启动器
type DarwinLauncher struct {
	PlatformLauncher
}

// newPlatformLauncher 创建macOS平台启动器
func newPlatformLauncher() AppLauncher {
	return &DarwinLauncher{}
}

// LaunchApp 智能启动应用（macOS实现）
func (dl *DarwinLauncher) LaunchApp(appIdentifier string) error {
	slog.Info("尝试启动macOS应用", "app", appIdentifier)

	// 特殊处理：浏览器启动
	if appIdentifier == "browser" {
		return dl.LaunchDefaultBrowser()
	}

	// 检查是否是第三方应用标记
	if strings.HasPrefix(appIdentifier, "THIRD_PARTY:") {
		appName := strings.TrimPrefix(appIdentifier, "THIRD_PARTY:")
		return dl.LaunchThirdPartyApp(appName)
	}

	// 尝试直接启动系统应用
	return dl.LaunchSystemApp(appIdentifier)
}

// LaunchSystemApp 启动系统内置应用
func (dl *DarwinLauncher) LaunchSystemApp(appName string) error {
	slog.Info("启动macOS系统应用", "app", appName)

	// 标准化应用名称
	normalizedName := dl.normalizeAppName(appName)

	// macOS系统应用映射
	systemApps := map[string]string{
		"notepad":    "TextEdit",
		"calc":       "Calculator",
		"calculator": "Calculator",
		"textedit":   "TextEdit",
		"finder":     "Finder",
		"safari":     "Safari",
		"terminal":   "Terminal",
	}

	if macApp, exists := systemApps[normalizedName]; exists {
		return dl.launchMacApp(macApp)
	}

	// 尝试直接启动
	return dl.launchMacApp(normalizedName)
}

// LaunchThirdPartyApp 启动第三方应用
func (dl *DarwinLauncher) LaunchThirdPartyApp(appName string) error {
	slog.Info("尝试启动macOS第三方应用", "app", appName)

	// 策略1: 尝试Applications目录
	if path, err := dl.findAppInApplications(appName); err == nil && path != "" {
		slog.Info("通过Applications目录找到应用", "app", appName, "path", path)
		return dl.launchByPath(path)
	}

	// 策略2: 尝试用户Applications目录
	if path, err := dl.findAppInUserApplications(appName); err == nil && path != "" {
		slog.Info("通过用户Applications目录找到应用", "app", appName, "path", path)
		return dl.launchByPath(path)
	}

	// 策略3: 尝试Homebrew安装的应用
	if path, err := dl.findAppInHomebrew(appName); err == nil && path != "" {
		slog.Info("通过Homebrew找到应用", "app", appName, "path", path)
		return dl.launchByPath(path)
	}

	// 策略4: 使用Spotlight搜索
	if path, err := dl.findAppWithSpotlight(appName); err == nil && path != "" {
		slog.Info("通过Spotlight找到应用", "app", appName, "path", path)
		return dl.launchByPath(path)
	}

	// 策略5: 使用跨平台搜索
	if path, err := dl.findAppWithSearch(appName); err == nil && path != "" {
		slog.Info("通过跨平台搜索找到应用", "app", appName, "path", path)
		return dl.launchByPath(path)
	}

	// 所有策略都失败，返回错误
	slog.Error("无法找到macOS第三方应用", "app", appName)
	return fmt.Errorf("无法找到应用: %s，建议检查应用是否已安装", appName)
}

// LaunchDefaultBrowser 启动默认浏览器（直接启动，避免递归）
func (dl *DarwinLauncher) LaunchDefaultBrowser() error {
	slog.Info("启动macOS默认浏览器（直接启动）")

	// 直接启动浏览器，避免递归调用退化机制
	urls := []string{
		"https://www.baidu.com",
		"https://www.google.com",
		"https://www.apple.com",
	}

	for _, url := range urls {
		cmd := exec.Command("open", url)
		err := cmd.Start()
		if err == nil {
			slog.Info("macOS默认浏览器启动成功", "url", url)
			return nil
		}
		slog.Debug("macOS浏览器URL失败", "url", url, "error", err)
	}

	slog.Error("macOS浏览器启动失败")
	return fmt.Errorf("启动默认浏览器失败")
}

// FindAppPath 查找应用程序路径
func (dl *DarwinLauncher) FindAppPath(appName string) (string, error) {
	// 尝试多种策略查找应用路径
	strategies := []func(string) (string, error){
		dl.findAppInApplications,
		dl.findAppInUserApplications,
		dl.findAppInHomebrew,
		dl.findAppWithSpotlight,
		dl.findAppWithSearch,
	}

	for _, strategy := range strategies {
		if path, err := strategy(appName); err == nil && path != "" {
			return path, nil
		}
	}

	return "", fmt.Errorf("未找到应用: %s", appName)
}

// launchMacApp 启动macOS应用
func (dl *DarwinLauncher) launchMacApp(appName string) error {
	// 尝试使用open命令启动应用
	cmd := exec.Command("open", "-a", appName)
	err := cmd.Start()
	if err != nil {
		slog.Error("macOS应用启动失败", "app", appName, "error", err)
		return fmt.Errorf("启动应用失败: %v", err)
	}

	slog.Info("macOS应用启动成功", "app", appName, "pid", cmd.Process.Pid)
	return nil
}

// findAppInApplications 在/Applications目录中查找应用
func (dl *DarwinLauncher) findAppInApplications(appName string) (string, error) {
	return dl.searchInDirectory("/Applications", appName)
}

// findAppInUserApplications 在用户Applications目录中查找应用
func (dl *DarwinLauncher) findAppInUserApplications(appName string) (string, error) {
	userAppsPath := filepath.Join(os.Getenv("HOME"), "Applications")
	return dl.searchInDirectory(userAppsPath, appName)
}

// findAppInHomebrew 在Homebrew安装目录中查找应用
func (dl *DarwinLauncher) findAppInHomebrew(appName string) (string, error) {
	homebrewPaths := []string{
		"/opt/homebrew/bin",
		"/usr/local/bin",
		"/opt/homebrew/Caskroom",
		"/usr/local/Caskroom",
	}

	for _, path := range homebrewPaths {
		if appPath, err := dl.searchInDirectory(path, appName); err == nil && appPath != "" {
			return appPath, nil
		}
	}

	return "", fmt.Errorf("未在Homebrew中找到应用")
}

// findAppWithSpotlight 使用Spotlight搜索应用
func (dl *DarwinLauncher) findAppWithSpotlight(appName string) (string, error) {
	// 使用mdfind命令调用Spotlight
	cmd := exec.Command("mdfind",
		"-onlyin", "/Applications",
		"-onlyin", "/System/Applications",
		fmt.Sprintf("kMDItemKind == 'Application' && kMDItemDisplayName == '*%s*'", appName))

	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("Spotlight搜索失败: %v", err)
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" && strings.HasSuffix(line, ".app") {
			return line, nil
		}
	}

	return "", fmt.Errorf("Spotlight未找到应用")
}

// searchInDirectory 在目录中搜索应用
func (dl *DarwinLauncher) searchInDirectory(dirPath, appName string) (string, error) {
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return "", err
	}

	for _, entry := range entries {
		fullPath := filepath.Join(dirPath, entry.Name())

		if entry.IsDir() && strings.HasSuffix(entry.Name(), ".app") {
			// macOS应用程序包
			if strings.Contains(strings.ToLower(entry.Name()), strings.ToLower(appName)) {
				return fullPath, nil
			}
		} else if !entry.IsDir() {
			// 可执行文件
			if strings.Contains(strings.ToLower(entry.Name()), strings.ToLower(appName)) {
				return fullPath, nil
			}
		}
	}

	return "", fmt.Errorf("未找到应用")
}

// launchByPath 重写以支持macOS应用包
func (dl *DarwinLauncher) launchByPath(path string) error {
	slog.Info("通过路径启动macOS应用", "path", path)

	var cmd *exec.Cmd
	if strings.HasSuffix(path, ".app") {
		// macOS应用程序包，使用open命令
		cmd = exec.Command("open", path)
	} else {
		// 普通可执行文件
		cmd = exec.Command(path)
	}

	err := cmd.Start()
	if err != nil {
		return fmt.Errorf("启动应用失败: %v", err)
	}

	slog.Info("macOS应用启动成功", "path", path, "pid", cmd.Process.Pid)
	return nil
}
