package service

import (
	"diandian/background/service/launcher"
)

// AppLauncher 应用启动器（兼容性包装）
// 为了保持向后兼容，保留原有的结构体名称
type AppLauncher struct {
	platformLauncher launcher.AppLauncher
}

// NewAppLauncher 创建应用启动器
func NewAppLauncher() *AppLauncher {
	return &AppLauncher{
		platformLauncher: launcher.NewAppLauncher(),
	}
}

// LaunchApp 启动应用程序
func (al *AppLauncher) LaunchApp(appIdentifier string) error {
	return al.platformLauncher.LaunchApp(appIdentifier)
}

// LaunchAppWithFallback 使用退化重试启动应用程序
func (al *AppLauncher) LaunchAppWithFallback(appIdentifier string) error {
	return al.platformLauncher.LaunchWithFallback(appIdentifier)
}
