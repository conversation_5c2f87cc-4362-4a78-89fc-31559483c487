//go:build windows

package launcher

import (
	"fmt"
	"log/slog"
	"os"
	"os/exec"
	"path/filepath"
	"strings"

	"golang.org/x/sys/windows/registry"
)

// WindowsLauncher Windows平台的应用启动器
type WindowsLauncher struct {
	PlatformLauncher
}

// newPlatformLauncher 创建Windows平台启动器
func newPlatformLauncher() AppLauncher {
	return &WindowsLauncher{}
}

// LaunchApp 智能启动应用（Windows实现）
func (wl *WindowsLauncher) LaunchApp(appIdentifier string) error {
	slog.Info("尝试启动Windows应用", "app", appIdentifier)

	// 特殊处理：浏览器启动
	if appIdentifier == "browser" {
		return wl.LaunchDefaultBrowser()
	}

	// 检查是否是第三方应用标记
	if strings.HasPrefix(appIdentifier, "THIRD_PARTY:") {
		appName := strings.TrimPrefix(appIdentifier, "THIRD_PARTY:")
		return wl.LaunchThirdPartyApp(appName)
	}

	// 尝试直接启动系统应用
	return wl.LaunchSystemApp(appIdentifier)
}

// LaunchSystemApp 启动系统内置应用
func (wl *WindowsLauncher) LaunchSystemApp(appName string) error {
	slog.Info("启动Windows系统应用", "app", appName)

	// 标准化应用名称
	normalizedName := wl.normalizeAppName(appName)

	cmd := exec.Command(normalizedName)
	err := cmd.Start()
	if err != nil {
		slog.Error("Windows系统应用启动失败", "app", appName, "error", err)
		return fmt.Errorf("启动系统应用失败: %v", err)
	}

	slog.Info("Windows系统应用启动成功", "app", appName, "pid", cmd.Process.Pid)
	return nil
}

// LaunchThirdPartyApp 启动第三方应用
func (wl *WindowsLauncher) LaunchThirdPartyApp(appName string) error {
	slog.Info("尝试启动Windows第三方应用", "app", appName)

	// 策略1: 尝试注册表查找
	if path, err := wl.findAppInRegistry(appName); err == nil && path != "" {
		slog.Info("通过注册表找到应用", "app", appName, "path", path)
		return wl.launchByPath(path)
	}

	// 策略2: 尝试常见安装路径
	if path, err := wl.findAppInCommonPaths(appName); err == nil && path != "" {
		slog.Info("通过常见路径找到应用", "app", appName, "path", path)
		return wl.launchByPath(path)
	}

	// 策略3: 尝试桌面快捷方式
	if path, err := wl.findAppInDesktop(appName); err == nil && path != "" {
		slog.Info("通过桌面快捷方式找到应用", "app", appName, "path", path)
		return wl.launchByPath(path)
	}

	// 策略4: 尝试开始菜单
	if path, err := wl.findAppInStartMenu(appName); err == nil && path != "" {
		slog.Info("通过开始菜单找到应用", "app", appName, "path", path)
		return wl.launchByPath(path)
	}

	// 策略5: 使用跨平台搜索
	if path, err := wl.findAppWithSearch(appName); err == nil && path != "" {
		slog.Info("通过跨平台搜索找到应用", "app", appName, "path", path)
		return wl.launchByPath(path)
	}

	// 所有策略都失败，返回错误
	slog.Error("无法找到Windows第三方应用", "app", appName)
	return fmt.Errorf("无法找到应用: %s，建议检查应用是否已安装", appName)
}

// LaunchDefaultBrowser 启动默认浏览器（直接启动，避免递归）
func (wl *WindowsLauncher) LaunchDefaultBrowser() error {
	slog.Info("启动Windows默认浏览器（直接启动）")

	// 直接启动浏览器，避免递归调用退化机制
	urls := []string{
		"https://www.baidu.com",
		"https://www.google.com",
		"https://www.bing.com",
	}

	for _, url := range urls {
		cmd := exec.Command("rundll32", "url.dll,FileProtocolHandler", url)
		err := cmd.Start()
		if err == nil {
			slog.Info("Windows默认浏览器启动成功", "url", url)
			return nil
		}
		slog.Debug("Windows浏览器URL失败", "url", url, "error", err)
	}

	slog.Error("Windows浏览器启动失败")
	return fmt.Errorf("启动默认浏览器失败")
}

// FindAppPath 查找应用程序路径
func (wl *WindowsLauncher) FindAppPath(appName string) (string, error) {
	// 尝试多种策略查找应用路径
	strategies := []func(string) (string, error){
		wl.findAppInRegistry,
		wl.findAppInCommonPaths,
		wl.findAppInDesktop,
		wl.findAppInStartMenu,
		wl.findAppWithSearch,
	}

	for _, strategy := range strategies {
		if path, err := strategy(appName); err == nil && path != "" {
			return path, nil
		}
	}

	return "", fmt.Errorf("未找到应用: %s", appName)
}

// findAppInRegistry 在注册表中查找应用
func (wl *WindowsLauncher) findAppInRegistry(appName string) (string, error) {
	// 查找已安装程序列表
	registryPaths := []string{
		`SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall`,
		`SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall`,
	}

	for _, regPath := range registryPaths {
		if path, err := wl.searchInRegistryPath(regPath, appName); err == nil && path != "" {
			return path, nil
		}
	}

	return "", fmt.Errorf("未在注册表中找到应用")
}

// searchInRegistryPath 在指定注册表路径中搜索
func (wl *WindowsLauncher) searchInRegistryPath(regPath, appName string) (string, error) {
	key, err := registry.OpenKey(registry.LOCAL_MACHINE, regPath, registry.ENUMERATE_SUB_KEYS)
	if err != nil {
		return "", err
	}
	defer key.Close()

	subkeys, err := key.ReadSubKeyNames(-1)
	if err != nil {
		return "", err
	}

	for _, subkey := range subkeys {
		subkeyPath := regPath + `\` + subkey
		if path, err := wl.checkRegistryEntry(subkeyPath, appName); err == nil && path != "" {
			return path, nil
		}
	}

	return "", fmt.Errorf("未找到")
}

// checkRegistryEntry 检查注册表条目
func (wl *WindowsLauncher) checkRegistryEntry(keyPath, appName string) (string, error) {
	key, err := registry.OpenKey(registry.LOCAL_MACHINE, keyPath, registry.QUERY_VALUE)
	if err != nil {
		return "", err
	}
	defer key.Close()

	// 检查显示名称
	if displayName, _, err := key.GetStringValue("DisplayName"); err == nil {
		if strings.Contains(strings.ToLower(displayName), strings.ToLower(appName)) {
			// 尝试获取安装路径
			if installLocation, _, err := key.GetStringValue("InstallLocation"); err == nil {
				return wl.findExecutableInPath(installLocation, appName)
			}
			// 尝试获取卸载字符串中的路径
			if uninstallString, _, err := key.GetStringValue("UninstallString"); err == nil {
				if exePath := wl.extractPathFromUninstallString(uninstallString); exePath != "" {
					return exePath, nil
				}
			}
		}
	}

	return "", fmt.Errorf("未找到匹配项")
}

// findAppInCommonPaths 在常见安装路径中查找应用
func (wl *WindowsLauncher) findAppInCommonPaths(appName string) (string, error) {
	commonPaths := []string{
		`C:\Program Files`,
		`C:\Program Files (x86)`,
		filepath.Join(os.Getenv("LOCALAPPDATA"), "Programs"),
		filepath.Join(os.Getenv("PROGRAMFILES"), "WindowsApps"),
	}

	for _, commonPath := range commonPaths {
		if path, err := wl.searchInDirectory(commonPath, appName, 2); err == nil && path != "" {
			return path, nil
		}
	}

	return "", fmt.Errorf("未在常见路径找到应用")
}

// findAppInDesktop 在桌面查找应用快捷方式
func (wl *WindowsLauncher) findAppInDesktop(appName string) (string, error) {
	desktopPaths := []string{
		filepath.Join(os.Getenv("USERPROFILE"), "Desktop"),
		filepath.Join(os.Getenv("PUBLIC"), "Desktop"),
	}

	for _, desktopPath := range desktopPaths {
		if path, err := wl.searchShortcutsInDirectory(desktopPath, appName); err == nil && path != "" {
			return path, nil
		}
	}

	return "", fmt.Errorf("未在桌面找到应用")
}

// findAppInStartMenu 在开始菜单中查找应用
func (wl *WindowsLauncher) findAppInStartMenu(appName string) (string, error) {
	startMenuPaths := []string{
		filepath.Join(os.Getenv("APPDATA"), "Microsoft", "Windows", "Start Menu", "Programs"),
		filepath.Join(os.Getenv("ALLUSERSPROFILE"), "Microsoft", "Windows", "Start Menu", "Programs"),
	}

	for _, startMenuPath := range startMenuPaths {
		if path, err := wl.searchShortcutsInDirectory(startMenuPath, appName); err == nil && path != "" {
			return path, nil
		}
	}

	return "", fmt.Errorf("未在开始菜单找到应用")
}

// 辅助方法

// searchInDirectory 在目录中搜索应用
func (wl *WindowsLauncher) searchInDirectory(dirPath, appName string, maxDepth int) (string, error) {
	if maxDepth <= 0 {
		return "", fmt.Errorf("达到最大搜索深度")
	}

	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return "", err
	}

	for _, entry := range entries {
		fullPath := filepath.Join(dirPath, entry.Name())

		if entry.IsDir() {
			// 检查目录名是否匹配
			if strings.Contains(strings.ToLower(entry.Name()), strings.ToLower(appName)) {
				if exePath, err := wl.findExecutableInPath(fullPath, appName); err == nil && exePath != "" {
					return exePath, nil
				}
			}
			// 递归搜索子目录
			if path, err := wl.searchInDirectory(fullPath, appName, maxDepth-1); err == nil && path != "" {
				return path, nil
			}
		} else {
			// 检查是否是可执行文件
			if strings.HasSuffix(strings.ToLower(entry.Name()), ".exe") {
				if strings.Contains(strings.ToLower(entry.Name()), strings.ToLower(appName)) {
					return fullPath, nil
				}
			}
		}
	}

	return "", fmt.Errorf("未找到")
}

// searchShortcutsInDirectory 在目录中搜索快捷方式
func (wl *WindowsLauncher) searchShortcutsInDirectory(dirPath, appName string) (string, error) {
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return "", err
	}

	for _, entry := range entries {
		if strings.HasSuffix(strings.ToLower(entry.Name()), ".lnk") {
			if strings.Contains(strings.ToLower(entry.Name()), strings.ToLower(appName)) {
				shortcutPath := filepath.Join(dirPath, entry.Name())
				if targetPath, err := wl.resolveShortcut(shortcutPath); err == nil && targetPath != "" {
					return targetPath, nil
				}
				// 如果无法解析快捷方式，返回快捷方式本身
				return shortcutPath, nil
			}
		}
	}

	return "", fmt.Errorf("未找到快捷方式")
}

// findExecutableInPath 在路径中查找可执行文件
func (wl *WindowsLauncher) findExecutableInPath(dirPath, appName string) (string, error) {
	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		return "", err
	}

	// 常见的可执行文件模式
	patterns := []string{
		appName + ".exe",
		strings.ToLower(appName) + ".exe",
		strings.ReplaceAll(appName, " ", "") + ".exe",
		strings.ReplaceAll(strings.ToLower(appName), " ", "") + ".exe",
	}

	for _, pattern := range patterns {
		execPath := filepath.Join(dirPath, pattern)
		if _, err := os.Stat(execPath); err == nil {
			return execPath, nil
		}
	}

	// 搜索子目录中的可执行文件
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return "", err
	}

	for _, entry := range entries {
		if strings.HasSuffix(strings.ToLower(entry.Name()), ".exe") {
			if strings.Contains(strings.ToLower(entry.Name()), strings.ToLower(appName)) {
				return filepath.Join(dirPath, entry.Name()), nil
			}
		}
	}

	return "", fmt.Errorf("未找到可执行文件")
}

// extractPathFromUninstallString 从卸载字符串中提取路径
func (wl *WindowsLauncher) extractPathFromUninstallString(uninstallString string) string {
	// 简化的路径提取逻辑
	// 实际项目中可能需要更复杂的解析
	if strings.Contains(uninstallString, ".exe") {
		parts := strings.Split(uninstallString, ".exe")
		if len(parts) > 0 {
			return strings.Trim(parts[0], `"`) + ".exe"
		}
	}
	return ""
}

// resolveShortcut 解析快捷方式获取目标路径
func (wl *WindowsLauncher) resolveShortcut(shortcutPath string) (string, error) {
	// Windows快捷方式解析比较复杂，这里简化处理
	// 实际项目中需要使用Windows API或第三方库来解析.lnk文件
	return shortcutPath, nil
}
