package platform

// ScreenInfo 屏幕信息
type ScreenInfo struct {
	Width  int
	Height int
}

// WindowPlatform 平台特定的窗口操作接口
type WindowPlatform interface {
	// GetScreenSize 获取屏幕尺寸
	GetScreenSize() ScreenInfo
	
	// GetPlatformWindowOptions 获取平台特定的窗口选项
	GetPlatformWindowOptions() interface{}
	
	// GetFloatingWindowOptions 获取浮动窗口的平台特定选项
	GetFloatingWindowOptions() interface{}
}

// NewWindowPlatform 创建平台特定的窗口管理器
func NewWindowPlatform() WindowPlatform {
	return newPlatformWindow()
}
