package app

import (
	"diandian/background/app/platform"
	"diandian/background/constant"
	"diandian/background/database"
	"diandian/background/util"

	"github.com/wailsapp/wails/v3/pkg/application"
	"github.com/wailsapp/wails/v3/pkg/events"
)

const (
	snapShow = 15 // 窗口贴边显示出来的像素
)

type WindowManager struct {
	app                *application.App
	winMap             map[string]*application.WebviewWindow
	floatingStickySide int // 浮动窗口贴边位置：0-无贴边，1-左，2-右，3-上
	platformWindow     platform.WindowPlatform

	initializeSuccess bool // 初始化是否成功
}

func (wm *WindowManager) Run() error {
	wm.GetWindow(WindowMain)
	return wm.app.Run()
}

func (wm *WindowManager) initializeMain() {
	win := wm.app.Window.NewWithOptions(application.WebviewWindowOptions{
		Title:     "点点小助理",
		Frameless: true, // 无边框窗口
		Width:     400,
		Height:    800,
		Mac: application.MacWindow{
			InvisibleTitleBarHeight: 50,
			Backdrop:                application.MacBackdropTranslucent,
			TitleBar:                application.MacTitleBarHiddenInset,
		},
		BackgroundColour: application.NewRGB(27, 38, 54),
		URL:              "/",
		DisableResize:    true,
		Windows:          wm.getPlatformWindowOptions(),
	})

	wm.winMap[WindowMain] = win
}

func (wm *WindowManager) initializeFloating() {
	win := wm.app.Window.NewWithOptions(application.WebviewWindowOptions{
		Title:          "点点飘啊飘",
		Frameless:      true, // 无边框窗口
		Width:          80,
		Height:         80,
		BackgroundType: application.BackgroundTypeTransparent,
		URL:            "/floating",
		AlwaysOnTop:    true,
		DisableResize:  true,
		Windows:        wm.getFloatingWindowOptions(),
	})

	win.RegisterHook(events.Common.WindowDidMove, func(event *application.WindowEvent) {
		rect := win.Bounds()
		// 判断窗口是否靠近屏幕边缘
		screenInfo := wm.platformWindow.GetScreenSize()
		screenWidth := screenInfo.Width
		screenHeight := screenInfo.Height

		oldStickySide := wm.floatingStickySide
		if rect.X <= 0 {
			// 左侧
			wm.floatingStickySide = 1
		} else if rect.X+rect.Width >= screenWidth {
			// 右侧
			wm.floatingStickySide = 2
		} else if rect.Y <= 0 {
			// 上方
			wm.floatingStickySide = 3
		} else if rect.Y+rect.Height >= screenHeight {
			// 下方（新增）
			wm.floatingStickySide = 4
		} else {
			wm.floatingStickySide = 0
		}

		// 如果贴边状态发生变化，发送事件通知前端更新
		if oldStickySide != wm.floatingStickySide {
			wm.EmitEvent(constant.EventStickySideChanged, wm.floatingStickySide)
		}
	})

	// 鼠标进入时显示窗口，这里只能用前端传入的自定义事件完成
	wm.app.Event.On(constant.EventMouseEnterFloating, func(event *application.CustomEvent) {
		switch wm.floatingStickySide {
		case 1:
			rect := win.Bounds()
			win.SetPosition(0, rect.Y)
		case 2:
			rect := win.Bounds()
			screenInfo := wm.platformWindow.GetScreenSize()
			win.SetPosition(screenInfo.Width-rect.Width, rect.Y)
		case 3:
			rect := win.Bounds()
			win.SetPosition(rect.X, 0)
		case 4:
			rect := win.Bounds()
			screenInfo := wm.platformWindow.GetScreenSize()
			win.SetPosition(rect.X, screenInfo.Height-rect.Height)
		}
	})

	// 鼠标离开时隐藏窗口
	wm.app.Event.On(constant.EventMouseLeaveFloating, func(event *application.CustomEvent) {
		switch wm.floatingStickySide {
		case 1:
			rect := win.Bounds()
			win.SetPosition(0-rect.Width+snapShow, rect.Y)
		case 2:
			rect := win.Bounds()
			screenInfo := wm.platformWindow.GetScreenSize()
			win.SetPosition(screenInfo.Width-snapShow, rect.Y)
		case 3:
			rect := win.Bounds()
			win.SetPosition(rect.X, 0-rect.Height+snapShow)
		case 4:
			rect := win.Bounds()
			screenInfo := wm.platformWindow.GetScreenSize()
			win.SetPosition(rect.X, screenInfo.Height-snapShow)
		}
	})

	wm.winMap[WindowFloating] = win
}

func (wm *WindowManager) initializeSettings() {
	win := wm.app.Window.NewWithOptions(application.WebviewWindowOptions{
		Title:     "点点配置项",
		Frameless: true, // 无边框窗口
		Width:     600,
		Height:    500,
		Mac: application.MacWindow{
			InvisibleTitleBarHeight: 50,
			Backdrop:                application.MacBackdropTranslucent,
			TitleBar:                application.MacTitleBarHiddenInset,
		},
		BackgroundColour: application.NewRGB(27, 38, 54),
		URL:              "/settings",
		DisableResize:    true,
		Windows:          wm.getPlatformWindowOptions(),
	})

	wm.winMap[WindowSettings] = win
}

func (wm *WindowManager) GetWindow(name string) *application.WebviewWindow {
	switch name {
	case WindowMain:
		if _, ok := wm.winMap[name]; !ok {
			wm.initializeMain()
		}
		return wm.winMap[WindowMain]
	case WindowFloating:
		if _, ok := wm.winMap[name]; !ok {
			wm.initializeFloating()
		}
		return wm.winMap[WindowFloating]
	case WindowSettings:
		if _, ok := wm.winMap[name]; !ok {
			wm.initializeSettings()
		}
		return wm.winMap[WindowSettings]
	default:
		return nil
	}
}

func (wm *WindowManager) ShowFloating() {
	wm.GetWindow(WindowMain).Hide()
	wm.GetWindow(WindowFloating).Show()
}

func (wm *WindowManager) ShowMain() {
	wm.GetWindow(WindowFloating).Hide()
	wm.GetWindow(WindowMain).Show()
}

func (wm *WindowManager) ShowSettings() {
	settingsWindow := wm.GetWindow(WindowSettings)
	if settingsWindow.IsVisible() {
		settingsWindow.Focus()
		return
	}
	settingsWindow.Show()
}

func (wm *WindowManager) HideSettings() {
	wm.GetWindow(WindowSettings).Hide()
}

// 右键菜单
func (wm *WindowManager) buildContextMenu() {
	contextMenu := application.NewContextMenu("floating-context-menu")

	click2ShowMain := contextMenu.Add("显示主界面")
	click2ShowMain.OnClick(func(ctx *application.Context) {
		wm.ShowMain()
	})
	click2Close := contextMenu.Add("关闭")
	click2Close.OnClick(func(ctx *application.Context) {
		wm.app.Quit()
	})

}

// 发送事件
func (wm *WindowManager) EmitEvent(name string, data any) {
	wm.app.Event.EmitEvent(&application.CustomEvent{
		Name: name,
		Data: data,
	})
}

// 程序启动时调用
func (wm *WindowManager) OnAppStart() {
	if err := util.InitializeSnowflake(); err != nil {
		return
	}
	if err := database.Initialize(); err != nil {
		return
	}
	wm.initializeSuccess = true
}

func (wm *WindowManager) IsInitializeSuccess() bool {
	return wm.initializeSuccess
}

func (wm *WindowManager) FloatingStickySide() int {
	return wm.floatingStickySide
}

// getPlatformWindowOptions 获取平台特定的窗口选项
func (wm *WindowManager) getPlatformWindowOptions() application.WindowsWindow {
	if options, ok := wm.platformWindow.GetPlatformWindowOptions().(application.WindowsWindow); ok {
		return options
	}
	// 如果不是Windows平台，返回空的WindowsWindow
	return application.WindowsWindow{}
}

// getFloatingWindowOptions 获取浮动窗口的平台特定选项
func (wm *WindowManager) getFloatingWindowOptions() application.WindowsWindow {
	if options, ok := wm.platformWindow.GetFloatingWindowOptions().(application.WindowsWindow); ok {
		return options
	}
	// 如果不是Windows平台，返回空的WindowsWindow
	return application.WindowsWindow{}
}
