package model

import (
	"encoding/json"
	"time"
)

type Step struct {
	Base
	TaskID          uint64     `json:"task_id,string" gorm:"index;not null"`
	StepIndex       int        `json:"step_index"`                        // 步骤序号
	Content         string     `json:"content" gorm:"type:text"`          // 展示给用户的消息内容
	StepType        string     `json:"step_type" gorm:"size:50"`          // message, action, screenshot, analysis
	Status          string     `json:"status" gorm:"size:20"`             // pending, running, completed, failed, skipped
	ActionType      string     `json:"action_type" gorm:"size:50"`        // click, type, key, scroll, wait
	Coordinates     string     `json:"coordinates" gorm:"size:100"`       // 操作坐标 (x,y)
	ActionData      string     `json:"action_data" gorm:"type:text"`      // 操作数据（如输入的文本、按键等）
	ExpectedOutcome string     `json:"expected_outcome" gorm:"type:text"` // 期望结果
	Screenshot      string     `json:"screenshot" gorm:"size:500"`        // 截图文件路径
	Result          string     `json:"result" gorm:"type:text"`           // 步骤执行结果
	ErrorMsg        string     `json:"error_msg" gorm:"type:text"`        // 错误信息
	RetryCount      int        `json:"retry_count" gorm:"default:0"`      // 重试次数
	MaxRetries      int        `json:"max_retries" gorm:"default:3"`      // 最大重试次数
	Priority        int        `json:"priority" gorm:"default:5"`         // 优先级 1-10
	Optional        bool       `json:"optional" gorm:"default:false"`     // 是否可选步骤
	StartTime       *time.Time `json:"start_time"`                        // 步骤开始时间
	EndTime         *time.Time `json:"end_time"`                          // 步骤结束时间
	Duration        int64      `json:"duration"`                          // 执行耗时(毫秒)
	Context         string     `json:"context" gorm:"type:text"`          // 上下文信息
	Metadata        string     `json:"metadata" gorm:"type:text"`         // 元数据JSON

	// 注意：关联关系通过外键建立，避免循环引用
}

// 步骤类型常量
const (
	StepTypeMessage    = "message"    // 消息显示
	StepTypeAction     = "action"     // 操作执行
	StepTypeScreenshot = "screenshot" // 截图
	StepTypeAnalysis   = "analysis"   // 分析
)

// 步骤状态常量
const (
	StepStatusPending   = "pending"
	StepStatusRunning   = "running"
	StepStatusCompleted = "completed"
	StepStatusFailed    = "failed"
	StepStatusSkipped   = "skipped"
)

// 操作类型常量
const (
	ActionTypeClick  = "click"
	ActionTypeType   = "type"
	ActionTypeKey    = "key"
	ActionTypeScroll = "scroll"
	ActionTypeWait   = "wait"
)

// GetMetadata 获取元数据
func (s *Step) GetMetadata() (map[string]interface{}, error) {
	if s.Metadata == "" {
		return make(map[string]interface{}), nil
	}
	var metadata map[string]interface{}
	if err := json.Unmarshal([]byte(s.Metadata), &metadata); err != nil {
		return nil, err
	}
	return metadata, nil
}

// SetMetadata 设置元数据
func (s *Step) SetMetadata(metadata interface{}) error {
	jsonData, err := json.Marshal(metadata)
	if err != nil {
		return err
	}
	s.Metadata = string(jsonData)
	return nil
}

// CanRetry 判断是否可以重试
func (s *Step) CanRetry() bool {
	return s.RetryCount < s.MaxRetries && s.Status == StepStatusFailed
}

// IncrementRetry 增加重试次数
func (s *Step) IncrementRetry() {
	s.RetryCount++
}

// MarkStarted 标记步骤开始
func (s *Step) MarkStarted() {
	now := time.Now()
	s.StartTime = &now
	s.Status = StepStatusRunning
}

// MarkCompleted 标记步骤完成
func (s *Step) MarkCompleted(success bool, result string, errorMsg string) {
	now := time.Now()
	s.EndTime = &now
	s.Result = result
	s.ErrorMsg = errorMsg

	if s.StartTime != nil {
		s.Duration = now.Sub(*s.StartTime).Milliseconds()
	}

	if success {
		s.Status = StepStatusCompleted
	} else {
		s.Status = StepStatusFailed
	}
}
