{"name": "frontend", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build:dev": "vue-tsc && vite build --minify false --mode development", "build": "vue-tsc && vite build --mode production", "preview": "vite preview"}, "dependencies": {"@heroicons/vue": "^2.2.0", "@wailsio/runtime": "^3.0.0-alpha.69", "element-plus": "^2.11.2", "vue": "^3.5.18", "vue-element-plus-x": "^1.3.7", "vue-router": "^4.5.1"}, "devDependencies": {"@tailwindcss/vite": "^4.1.13", "@tsconfig/node22": "^22.0.2", "@types/node": "^22.16.5", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.31.0", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "prettier": "3.6.2", "tailwind-scrollbar": "^4.0.2", "tailwindcss": "^4.1.13", "typescript": "~5.8.0", "unplugin-auto-import": "^20.1.0", "unplugin-vue-components": "^29.0.0", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0", "vue-tsc": "^3.0.4"}}