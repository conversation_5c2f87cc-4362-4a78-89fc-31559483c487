// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import { Call as $Call, CancellablePromise as $CancellablePromise, Create as $Create } from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as model$0 from "../model/models.js";

export function AllSettings(): $CancellablePromise<(model$0.Setting | null)[]> {
    return $Call.ByID(3595887451).then(($result: any) => {
        return $$createType2($result);
    });
}

export function CanWork(): $CancellablePromise<boolean> {
    return $Call.ByID(1075064858);
}

export function GetThemeSetting(): $CancellablePromise<model$0.Setting | null> {
    return $Call.ByID(919430558).then(($result: any) => {
        return $$createType1($result);
    });
}

export function SaveSetting(setting: model$0.Setting | null): $CancellablePromise<void> {
    return $Call.ByID(2761623378, setting);
}

// Private type creation functions
const $$createType0 = model$0.Setting.createFrom;
const $$createType1 = $Create.Nullable($$createType0);
const $$createType2 = $Create.Array($$createType1);
