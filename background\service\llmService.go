package service

import (
	"context"
	"fmt"
	"log/slog"
	"strings"

	"diandian/background/constant"
	"diandian/background/database"
	"diandian/background/domain"
	"diandian/background/model"
	"diandian/background/service/operation"

	"github.com/sa<PERSON><PERSON>nov/go-openai"
	"github.com/sashabaranov/go-openai/jsonschema"
)

var DefaultLLMService = &LLMService{}

type LLMService struct{}

// ===== 使用domain包中的统一定义，避免重复 =====
// 所有配置和响应类型都使用domain包中的定义：
// - domain.UserMessageAnalysisResponse (替代UnifiedMessageResponse)
// - domain.TextModelConfig
// - domain.VisionModelConfig

// GetTextModelConfig 获取文本模型配置 (公开方法用于测试)
func (s *LLMService) GetTextModelConfig() (*domain.TextModelConfig, error) {
	return s.getTextModelConfig()
}

// 获取文本模型配置
func (s *LLMService) getTextModelConfig() (*domain.TextModelConfig, error) {
	var settings []*model.Setting
	err := database.DB.Where("key IN ?", []string{
		model.SettingKeyLlmTextBaseUrl,
		model.SettingKeyLlmTextToken,
		model.SettingKeyLlmTextModel,
	}).Find(&settings).Error

	if err != nil {
		return nil, fmt.Errorf("获取文本模型配置失败: %v", err)
	}

	config := &domain.TextModelConfig{}
	for _, setting := range settings {
		if setting.Value == nil {
			continue
		}
		switch setting.Key {
		case model.SettingKeyLlmTextBaseUrl:
			config.BaseURL = *setting.Value
		case model.SettingKeyLlmTextToken:
			config.Token = *setting.Value
		case model.SettingKeyLlmTextModel:
			config.Model = *setting.Value
		}
	}

	if config.Token == "" || config.Model == "" {
		return nil, fmt.Errorf("文本模型配置不完整")
	}

	return config, nil
}

// GetVisionModelConfig 获取视觉模型配置 (公开方法用于测试)
func (s *LLMService) GetVisionModelConfig() (*domain.VisionModelConfig, error) {
	return s.getVisionModelConfig()
}

// 获取视觉模型配置
func (s *LLMService) getVisionModelConfig() (*domain.VisionModelConfig, error) {
	var settings []*model.Setting
	err := database.DB.Where("key IN ?", []string{
		model.SettingKeyLlmVlBaseUrl,
		model.SettingKeyLlmVlToken,
		model.SettingKeyLlmVlModel,
	}).Find(&settings).Error

	if err != nil {
		return nil, fmt.Errorf("获取视觉模型配置失败: %v", err)
	}

	config := &domain.VisionModelConfig{}
	for _, setting := range settings {
		if setting.Value == nil {
			continue
		}
		switch setting.Key {
		case model.SettingKeyLlmVlBaseUrl:
			config.BaseURL = *setting.Value
		case model.SettingKeyLlmVlToken:
			config.Token = *setting.Value
		case model.SettingKeyLlmVlModel:
			config.Model = *setting.Value
		}
	}

	if config.Token == "" || config.Model == "" {
		return nil, fmt.Errorf("视觉模型配置不完整")
	}

	return config, nil
}

// CreateTextClient 创建文本模型客户端 (公开方法用于测试)
func (s *LLMService) CreateTextClient() (*openai.Client, string, error) {
	return s.createTextClient()
}

// 创建文本模型客户端
func (s *LLMService) createTextClient() (*openai.Client, string, error) {
	config, err := s.getTextModelConfig()
	if err != nil {
		return nil, "", fmt.Errorf("获取文本模型配置失败: %v", err)
	}

	clientConfig := openai.DefaultConfig(config.Token)
	if config.BaseURL != "" {
		clientConfig.BaseURL = config.BaseURL
	}

	client := openai.NewClientWithConfig(clientConfig)
	return client, config.Model, nil
}

// CreateVisionClient 创建视觉模型客户端 (公开方法用于测试)
func (s *LLMService) CreateVisionClient() (*openai.Client, string, error) {
	return s.createVisionClient()
}

// 创建视觉模型客户端
func (s *LLMService) createVisionClient() (*openai.Client, string, error) {
	config, err := s.getVisionModelConfig()
	if err != nil {
		return nil, "", fmt.Errorf("获取视觉模型配置失败: %v", err)
	}

	clientConfig := openai.DefaultConfig(config.Token)
	if config.BaseURL != "" {
		clientConfig.BaseURL = config.BaseURL
	}

	client := openai.NewClientWithConfig(clientConfig)
	return client, config.Model, nil
}

// cleanMarkdownCodeBlock 清理markdown代码块标记和其他格式标记
func cleanMarkdownCodeBlock(content string) string {
	content = strings.TrimSpace(content)

	// 移除开头的各种markdown代码块标记
	patterns := []string{
		"```json",
		"```JSON",
		"```javascript",
		"```js",
		"```",
		"``",
		"`",
	}

	for _, pattern := range patterns {
		if strings.HasPrefix(content, pattern) {
			content = content[len(pattern):]
			break
		}
	}

	// 移除结尾的各种markdown标记
	endPatterns := []string{
		"```",
		"``",
		"`",
	}

	for _, pattern := range endPatterns {
		if strings.HasSuffix(content, pattern) {
			content = content[:len(content)-len(pattern)]
			break
		}
	}

	// 移除可能的语言标识符行
	lines := strings.Split(content, "\n")
	if len(lines) > 0 {
		firstLine := strings.TrimSpace(lines[0])
		// 如果第一行只包含语言标识符，移除它
		if firstLine == "json" || firstLine == "JSON" || firstLine == "javascript" || firstLine == "js" {
			lines = lines[1:]
			content = strings.Join(lines, "\n")
		}
	}

	// 移除多余的空白字符
	content = strings.TrimSpace(content)

	// 移除可能的BOM标记
	if strings.HasPrefix(content, "\ufeff") {
		content = content[3:]
	}

	return content
}

// retryLLMCall 重试LLM调用的通用方法
func (s *LLMService) retryLLMCall(
	callFunc func() (string, error),
	validateFunc func(content string) error,
	maxRetries int,
	operation string,
) (string, error) {
	var lastErr error

	for attempt := 1; attempt <= maxRetries; attempt++ {
		// 调用LLM
		content, err := callFunc()
		if err != nil {
			lastErr = fmt.Errorf("LLM调用失败: %v", err)
			if attempt < maxRetries {
				fmt.Printf("🔄 %s第%d次尝试失败，重试中... 错误: %v\n", operation, attempt, err)
				continue
			}
			break
		}

		// 清理内容
		cleanedContent := cleanMarkdownCodeBlock(content)

		// 验证内容
		if validateFunc != nil {
			if err := validateFunc(cleanedContent); err != nil {
				lastErr = fmt.Errorf("内容验证失败: %v", err)
				if attempt < maxRetries {
					fmt.Printf("🔄 %s第%d次尝试验证失败，重试中... 错误: %v\n", operation, attempt, err)
					continue
				}
				break
			}
		}

		// 成功
		if attempt > 1 {
			fmt.Printf("✅ %s在第%d次尝试后成功\n", operation, attempt)
		}
		return cleanedContent, nil
	}

	return "", fmt.Errorf("%s在%d次尝试后仍然失败，最后错误: %v", operation, maxRetries, lastErr)
}

// 简单的文本聊天接口
func (s *LLMService) SimpleChat(userMessage string) (string, error) {
	client, model, err := s.createTextClient()
	if err != nil {
		return "", err
	}

	resp, err := client.CreateChatCompletion(
		context.Background(),
		openai.ChatCompletionRequest{
			Model: model,
			Messages: []openai.ChatCompletionMessage{
				{
					Role:    openai.ChatMessageRoleUser,
					Content: userMessage,
				},
			},
			MaxTokens:   2000,
			Temperature: 0.7,
		},
	)

	if err != nil {
		return "", fmt.Errorf("调用LLM失败: %v", err)
	}

	if len(resp.Choices) == 0 {
		return "", fmt.Errorf("API返回空响应")
	}

	return resp.Choices[0].Message.Content, nil
}

// 统一处理用户消息：同时进行聊天回复和任务判断
func (s *LLMService) ProcessMessage(conversationID uint64) (*model.Message, *domain.UserMessageAnalysisResponse, error) {
	client, m, err := s.createTextClient()
	if err != nil {
		slog.Error("创建文本模型客户端失败", "error", err)
		return nil, nil, err
	}

	messages := []openai.ChatCompletionMessage{
		{
			Role:    openai.ChatMessageRoleSystem,
			Content: constant.PromptAnalyzeUserMessage,
		},
	}

	// 获取所有历史消息
	var msgs []*model.Message
	err = database.DB.Where("conversation_id = ?", conversationID).Order("created_at asc").Find(&msgs).Error
	if err != nil {
		return nil, nil, err
	}

	// 构造对话消息
	for _, msg := range msgs {
		role := openai.ChatMessageRoleUser
		if msg.Role == model.MessageRoleAssistant {
			role = openai.ChatMessageRoleAssistant
		}
		messages = append(messages, openai.ChatCompletionMessage{
			Role:    role,
			Content: msg.Content,
		})
	}

	var result domain.UserMessageAnalysisResponse
	schema, err := jsonschema.GenerateSchemaForType(result)
	if err != nil {
		slog.Error("生成大模型schema规范失败", "error", err)
		return nil, nil, err
	}

	slog.Debug("准备调用大模型消息处理API")

	resp, err := client.CreateChatCompletion(
		context.Background(),
		openai.ChatCompletionRequest{
			Model:    m,
			Messages: messages,
			// MaxTokens:   1500,
			// Temperature: 0.3,
			ResponseFormat: &openai.ChatCompletionResponseFormat{
				Type: openai.ChatCompletionResponseFormatTypeJSONSchema,
				JSONSchema: &openai.ChatCompletionResponseFormatJSONSchema{
					Name:   "UserMessageAnalysisResponse",
					Schema: schema,
					Strict: true,
				},
			},
		},
	)

	if err != nil {
		slog.Error("调用消息处理API失败", "error", err)
		return nil, nil, fmt.Errorf("调用消息处理API失败: %v", err)
	}

	if len(resp.Choices) == 0 {
		slog.Error("消息处理API返回空响应")
		return nil, nil, fmt.Errorf("消息处理API返回空响应")
	}

	slog.Debug("消息处理API返回", "content", resp.Choices[0].Message.Content)

	// 清理markdown标记并解析JSON响应
	cleanedContent := cleanMarkdownCodeBlock(resp.Choices[0].Message.Content)
	slog.Debug("清理后的内容", "cleaned_content", cleanedContent)

	err = schema.Unmarshal(cleanedContent, &result)
	if err != nil {
		slog.Error("解析消息处理结果失败",
			"error", err,
			"raw_content", resp.Choices[0].Message.Content,
			"cleaned_content", cleanedContent)
		return nil, nil, fmt.Errorf("解析消息处理结果失败: %v", err)
	}

	// 记录返回结果
	msg := &model.Message{
		ConversationID: conversationID,
		Role:           model.MessageRoleAssistant,
		Content:        resp.Choices[0].Message.Content,
	}
	database.DB.Create(msg)

	return msg, &result, nil
}

// 使用domain包中的定义，避免重复
// type AutomationTaskDecomposition = domain.AutomationTaskDecomposition
// type AutomationStepPlan = domain.AutomationStepPlan

// ===== 使用domain包中的统一定义，避免重复 =====
// 所有操作类型都使用domain包中的定义：
// - domain.ClickOperation
// - domain.TypeOperation
// - domain.LaunchAppOperation
// - domain.FileOperation
// - domain.ScreenshotOperation
// - domain.ClipboardOperation
// - domain.WaitOperation
// - domain.KeyPressOperation
// - domain.OperationResponse

// ===== 使用domain包中的视觉分析定义，避免重复 =====
// 所有视觉分析相关类型都使用domain包中的定义：
// - domain.VisualAnalysisResponse
// - domain.VisualElement
// - domain.Coordinates
// - domain.ScreenInfo
// - domain.ActionRecommendation

// 分析自动化任务并分解为具体步骤
func (s *LLMService) DecomposeAutomationTask(conversationHistory []openai.ChatCompletionMessage) (*domain.AutomationTaskDecomposition, error) {
	client, model, err := s.createTextClient()
	if err != nil {
		return nil, err
	}

	messages := []openai.ChatCompletionMessage{
		{
			Role:    openai.ChatMessageRoleSystem,
			Content: constant.PromptAutomationTaskDecomposition,
		},
	}

	// 添加对话历史
	messages = append(messages, conversationHistory...)

	var result domain.AutomationTaskDecomposition
	schema, err := jsonschema.GenerateSchemaForType(result)
	if err != nil {
		return nil, fmt.Errorf("生成schema失败: %v", err)
	}

	// 定义LLM调用函数
	callFunc := func() (string, error) {
		resp, err := client.CreateChatCompletion(
			context.Background(),
			openai.ChatCompletionRequest{
				Model:    model,
				Messages: messages,
				ResponseFormat: &openai.ChatCompletionResponseFormat{
					Type: openai.ChatCompletionResponseFormatTypeJSONSchema,
					JSONSchema: &openai.ChatCompletionResponseFormatJSONSchema{
						Name:   "AutomationTaskDecomposition",
						Schema: schema,
						Strict: true,
					},
				},
			},
		)

		if err != nil {
			return "", err
		}

		if len(resp.Choices) == 0 {
			return "", fmt.Errorf("API返回空响应")
		}

		return resp.Choices[0].Message.Content, nil
	}

	// 定义验证函数
	validateFunc := func(content string) error {
		var tempResult domain.AutomationTaskDecomposition
		if err := schema.Unmarshal(content, &tempResult); err != nil {
			// 记录原始LLM返回内容用于排查
			slog.Error("任务分解JSON解析失败",
				"error", err,
				"raw_content", content,
				"content_length", len(content))
			return fmt.Errorf("JSON schema验证失败: %v", err)
		}

		// 验证必要字段
		if tempResult.TaskType == "" {
			slog.Error("任务分解验证失败：缺少task_type", "content", content)
			return fmt.Errorf("缺少task_type字段")
		}
		if tempResult.Description == "" {
			slog.Error("任务分解验证失败：缺少description", "content", content)
			return fmt.Errorf("缺少description字段")
		}
		if len(tempResult.Steps) == 0 {
			slog.Error("任务分解验证失败：缺少steps", "content", content)
			return fmt.Errorf("缺少steps字段或步骤为空")
		}

		// 验证每个步骤
		for i, step := range tempResult.Steps {
			if step.Type == "" {
				slog.Error("任务分解验证失败：步骤缺少type",
					"step_index", i+1,
					"step", step,
					"content", content)
				return fmt.Errorf("步骤%d缺少type字段", i+1)
			}
			if step.Description == "" {
				slog.Error("任务分解验证失败：步骤缺少description",
					"step_index", i+1,
					"step", step,
					"content", content)
				return fmt.Errorf("步骤%d缺少description字段", i+1)
			}
		}

		// 记录成功的解析结果
		slog.Info("任务分解验证成功",
			"task_type", tempResult.TaskType,
			"steps_count", len(tempResult.Steps),
			"risk_level", tempResult.RiskLevel)

		return nil
	}

	// 使用重试机制调用LLM
	content, err := s.retryLLMCall(callFunc, validateFunc, 3, "任务分解")
	if err != nil {
		return nil, fmt.Errorf("任务分解失败: %v", err)
	}

	// 最终解析 - 再次清理确保万无一失
	finalCleanedContent := cleanMarkdownCodeBlock(content)
	slog.Debug("最终清理后的内容", "final_cleaned_content", finalCleanedContent)

	err = schema.Unmarshal(finalCleanedContent, &result)
	if err != nil {
		slog.Error("解析任务分解结果失败",
			"error", err,
			"original_content", content,
			"final_cleaned_content", finalCleanedContent)
		return nil, fmt.Errorf("解析任务分解结果失败: %v", err)
	}

	return &result, nil
}

// 分析屏幕截图
func (s *LLMService) AnalyzeScreenshot(imageData []byte, analysisRequest string) (*domain.VisualAnalysisResponse, error) {
	generator := operation.NewVisionGenerator()

	result, err := generator.Analyze(imageData, analysisRequest)
	if err != nil {
		return nil, err
	}

	// 转换回原类型
	elements := make([]domain.VisualElement, len(result.ElementsFound))
	for i, elem := range result.ElementsFound {
		elements[i] = domain.VisualElement{
			Type:        elem.Type,
			Description: elem.Description,
			Coordinates: domain.Coordinates{
				X:      elem.Coordinates.X,
				Y:      elem.Coordinates.Y,
				Width:  elem.Coordinates.Width,
				Height: elem.Coordinates.Height,
			},
			Confidence:  elem.Confidence,
			TextContent: elem.TextContent,
			Clickable:   elem.Clickable,
		}
	}

	return &domain.VisualAnalysisResponse{
		ElementsFound:   elements,
		ScreenInfo:      domain.ScreenInfo{},    // 保持空的ScreenInfo以兼容现有代码
		Recommendations: result.Recommendations, // 直接使用，无需转换
	}, nil
}

// ===== 第二阶段：具体操作生成方法 =====

// 生成点击操作
func (s *LLMService) GenerateClickOperation(contextInfo string, screenAnalysis *domain.VisualAnalysisResponse) (*domain.ClickOperation, error) {
	generator := operation.NewClickGenerator()

	// 直接使用domain类型，无需转换
	result, err := generator.Generate(contextInfo, screenAnalysis)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// 生成输入操作
func (s *LLMService) GenerateTypeOperation(contextInfo string) (*domain.TypeOperation, error) {
	generator := operation.NewTypeGenerator()

	result, err := generator.Generate(contextInfo)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// 生成文件操作
func (s *LLMService) GenerateFileOperation(contextInfo string) (*domain.FileOperation, error) {
	generator := operation.NewFileGenerator()

	result, err := generator.Generate(contextInfo)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// GenerateFallbackOperation 生成保底操作
func (s *LLMService) GenerateFallbackOperation(prompt string) (string, error) {
	config, err := s.getTextModelConfig()
	if err != nil {
		return "", fmt.Errorf("获取文本模型配置失败: %v", err)
	}

	clientConfig := openai.DefaultConfig(config.Token)
	clientConfig.BaseURL = config.BaseURL
	client := openai.NewClientWithConfig(clientConfig)

	messages := []openai.ChatCompletionMessage{
		{
			Role: openai.ChatMessageRoleSystem,
			Content: `你是一个专业的自动化操作助手。你的任务是根据屏幕分析结果生成具体的鼠标键盘操作指令。

要求：
1. 必须返回有效的JSON格式
2. 操作必须安全可靠
3. 坐标必须在合理范围内
4. 提供详细的推理过程
5. 给出操作的置信度评估

支持的操作类型：
- click: 鼠标点击操作
- type: 文本输入操作
- key: 键盘按键操作
- scroll: 滚动操作
- wait: 等待操作`,
		},
		{
			Role:    openai.ChatMessageRoleUser,
			Content: prompt,
		},
	}

	resp, err := client.CreateChatCompletion(context.Background(), openai.ChatCompletionRequest{
		Model:       config.Model,
		Messages:    messages,
		MaxTokens:   1000,
		Temperature: 0.3, // 较低的温度以获得更稳定的结果
	})

	if err != nil {
		return "", fmt.Errorf("调用LLM失败: %v", err)
	}

	if len(resp.Choices) == 0 {
		return "", fmt.Errorf("LLM返回空响应")
	}

	response := strings.TrimSpace(resp.Choices[0].Message.Content)
	slog.Info("LLM生成保底操作响应", "response_length", len(response))

	return response, nil
}

// GenerateTaskReplan 生成任务重新规划
func (s *LLMService) GenerateTaskReplan(prompt string) (string, error) {
	config, err := s.getTextModelConfig()
	if err != nil {
		return "", fmt.Errorf("获取文本模型配置失败: %v", err)
	}

	clientConfig := openai.DefaultConfig(config.Token)
	clientConfig.BaseURL = config.BaseURL
	client := openai.NewClientWithConfig(clientConfig)

	messages := []openai.ChatCompletionMessage{
		{
			Role: openai.ChatMessageRoleSystem,
			Content: `你是一个专业的任务规划助手。你的任务是根据失败的步骤重新规划后续的执行步骤。

要求：
1. 分析失败原因并提供替代方案
2. 生成具体可执行的步骤序列
3. 每个步骤必须包含类型、描述和上下文
4. 确保步骤能够达到原始期望结果
5. 返回有效的JSON格式

步骤类型包括：
- click: 鼠标点击操作
- type: 文本输入操作
- key_press: 键盘按键操作
- launch_app: 启动应用程序
- screenshot: 截图操作
- wait: 等待操作
- file: 文件操作
- clipboard: 剪贴板操作`,
		},
		{
			Role:    openai.ChatMessageRoleUser,
			Content: prompt,
		},
	}

	resp, err := client.CreateChatCompletion(context.Background(), openai.ChatCompletionRequest{
		Model:       config.Model,
		Messages:    messages,
		MaxTokens:   2000,
		Temperature: 0.4, // 稍高的温度以获得更有创意的规划
	})

	if err != nil {
		return "", fmt.Errorf("调用LLM失败: %v", err)
	}

	if len(resp.Choices) == 0 {
		return "", fmt.Errorf("LLM返回空响应")
	}

	response := strings.TrimSpace(resp.Choices[0].Message.Content)
	slog.Info("LLM生成任务重新规划响应", "response_length", len(response))

	return response, nil
}
